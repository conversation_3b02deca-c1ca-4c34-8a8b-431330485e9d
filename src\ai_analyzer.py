"""
🤖 AI Analyzer Module
Advanced AI analysis using multiple machine learning models
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class AIAnalyzer:
    """Advanced AI analyzer with multiple ML models"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.is_initialized = False
        self.prediction_cache = {}
        
        # Model configurations
        self.lstm_config = {
            'sequence_length': 60,
            'features': ['Close', 'Volume', 'RSI', 'MACD', 'BB_Position', 'ATR', 'DXY', 'VIX'],
            'epochs': 100,
            'batch_size': 32
        }
        
        self.rf_config = {
            'n_estimators': 200,
            'max_depth': 15,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'random_state': 42
        }
    
    async def initialize_models(self):
        """Initialize all AI models"""
        try:
            logger.info("🤖 Initializing AI models...")

            # Simplified initialization without ML models
            self.is_initialized = True
            logger.info("✅ AI models initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing models: {e}")
            raise e
    
    def _create_simple_model(self):
        """Create simple prediction model using technical analysis"""
        # Simplified model without ML dependencies
        return None
    
    async def analyze_complete(self, data: pd.DataFrame) -> Dict:
        """Complete AI analysis with all models"""
        try:
            if not self.is_initialized:
                await self.initialize_models()
            
            logger.info("🔍 Running complete AI analysis...")
            
            # Prepare data
            processed_data = self._prepare_data(data)
            
            # Run all analyses
            results = {
                'price_prediction': await self._predict_price(processed_data),
                'trend_analysis': await self._analyze_trend(processed_data),
                'support_resistance': await self._find_support_resistance(data),
                'pattern_recognition': await self._recognize_patterns(data),
                'sentiment_analysis': await self._analyze_market_sentiment(data),
                'volatility_analysis': await self._analyze_volatility(data),
                'momentum_analysis': await self._analyze_momentum(data),
                'volume_analysis': await self._analyze_volume(data),
                'confidence_score': 0.0,
                'timestamp': datetime.now().isoformat()
            }
            
            # Calculate overall confidence
            results['confidence_score'] = self._calculate_confidence(results)
            
            # Cache results
            self.prediction_cache['complete'] = results
            
            logger.info("✅ Complete analysis finished")
            return results
            
        except Exception as e:
            logger.error(f"Error in complete analysis: {e}")
            return self._get_fallback_analysis()
    
    async def quick_analysis(self, current_data: Dict) -> Dict:
        """Quick analysis for real-time updates"""
        try:
            # Simple technical analysis
            price = current_data.get('price', 0)
            volume = current_data.get('volume', 0)
            daily_change_pct = current_data.get('daily_change_pct', 0)
            
            # Quick trend determination
            if daily_change_pct > 1:
                trend = "Strong Bullish"
                trend_strength = min(abs(daily_change_pct) / 2, 5)
            elif daily_change_pct > 0.3:
                trend = "Bullish"
                trend_strength = min(abs(daily_change_pct) / 1, 3)
            elif daily_change_pct < -1:
                trend = "Strong Bearish"
                trend_strength = min(abs(daily_change_pct) / 2, 5)
            elif daily_change_pct < -0.3:
                trend = "Bearish"
                trend_strength = min(abs(daily_change_pct) / 1, 3)
            else:
                trend = "Sideways"
                trend_strength = 1
            
            # Quick signals
            signals = []
            if daily_change_pct > 0.5:
                signals.append("Momentum Building")
            if volume > 0:
                signals.append("Volume Confirmation")
            
            return {
                'price': price,
                'trend': trend,
                'trend_strength': trend_strength,
                'signals': signals,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in quick analysis: {e}")
            return {'error': str(e)}
    
    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for ML models"""
        try:
            # Select features
            feature_columns = [col for col in self.lstm_config['features'] if col in data.columns]
            processed_data = data[feature_columns].copy()
            
            # Handle missing values
            processed_data = processed_data.fillna(method='ffill').fillna(method='bfill')
            
            # Add derived features
            processed_data['Price_Change'] = processed_data['Close'].pct_change()
            processed_data['Volume_Change'] = processed_data['Volume'].pct_change()
            processed_data['High_Low_Ratio'] = data['High'] / data['Low'] if 'High' in data.columns and 'Low' in data.columns else 1
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error preparing data: {e}")
            return data
    
    async def _predict_price(self, data: pd.DataFrame) -> Dict:
        """Predict future price using simple technical analysis"""
        try:
            if len(data) < 10:
                return {'error': 'Insufficient data for prediction'}

            current_price = float(data['Close'].iloc[-1])

            # Simple prediction based on moving averages and momentum
            if 'SMA_20' in data.columns and 'SMA_50' in data.columns:
                sma_20 = float(data['SMA_20'].iloc[-1])
                sma_50 = float(data['SMA_50'].iloc[-1])

                # Simple trend-based prediction
                if sma_20 > sma_50:
                    # Bullish trend - predict slight increase
                    predicted_price = current_price * 1.002  # 0.2% increase
                    confidence = 0.6
                elif sma_20 < sma_50:
                    # Bearish trend - predict slight decrease
                    predicted_price = current_price * 0.998  # 0.2% decrease
                    confidence = 0.6
                else:
                    # Sideways - predict no change
                    predicted_price = current_price
                    confidence = 0.4
            else:
                # Fallback prediction
                predicted_price = current_price
                confidence = 0.3

            price_change = predicted_price - current_price
            price_change_pct = (price_change / current_price) * 100

            return {
                'current_price': current_price,
                'predicted_price': predicted_price,
                'price_change': price_change,
                'price_change_pct': price_change_pct,
                'confidence': confidence,
                'method': 'Technical Analysis'
            }

        except Exception as e:
            logger.error(f"Error in price prediction: {e}")
            return {'error': str(e)}
    
    async def _analyze_trend(self, data: pd.DataFrame) -> Dict:
        """Analyze market trend using multiple indicators"""
        try:
            latest = data.iloc[-1]
            
            # Moving average trend
            ma_trend = "Neutral"
            if 'SMA_20' in data.columns and 'SMA_50' in data.columns:
                if latest['Close'] > latest['SMA_20'] > latest['SMA_50']:
                    ma_trend = "Bullish"
                elif latest['Close'] < latest['SMA_20'] < latest['SMA_50']:
                    ma_trend = "Bearish"
            
            # MACD trend
            macd_trend = "Neutral"
            if 'MACD' in data.columns and 'MACD_Signal' in data.columns:
                if latest['MACD'] > latest['MACD_Signal']:
                    macd_trend = "Bullish"
                elif latest['MACD'] < latest['MACD_Signal']:
                    macd_trend = "Bearish"
            
            # RSI analysis
            rsi_signal = "Neutral"
            if 'RSI' in data.columns:
                rsi = latest['RSI']
                if rsi > 70:
                    rsi_signal = "Overbought"
                elif rsi < 30:
                    rsi_signal = "Oversold"
                elif rsi > 50:
                    rsi_signal = "Bullish"
                else:
                    rsi_signal = "Bearish"
            
            # Overall trend
            bullish_signals = sum([
                ma_trend == "Bullish",
                macd_trend == "Bullish",
                rsi_signal == "Bullish"
            ])
            
            bearish_signals = sum([
                ma_trend == "Bearish",
                macd_trend == "Bearish",
                rsi_signal == "Bearish"
            ])
            
            if bullish_signals > bearish_signals:
                overall_trend = "Bullish"
                trend_strength = bullish_signals / 3
            elif bearish_signals > bullish_signals:
                overall_trend = "Bearish"
                trend_strength = bearish_signals / 3
            else:
                overall_trend = "Neutral"
                trend_strength = 0.5
            
            return {
                'overall_trend': overall_trend,
                'trend_strength': trend_strength,
                'ma_trend': ma_trend,
                'macd_trend': macd_trend,
                'rsi_signal': rsi_signal,
                'rsi_value': float(latest.get('RSI', 50))
            }
            
        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            return {'error': str(e)}
    
    async def _find_support_resistance(self, data: pd.DataFrame) -> Dict:
        """Find support and resistance levels"""
        try:
            if 'High' not in data.columns or 'Low' not in data.columns:
                return {'error': 'High/Low data not available'}
            
            # Get recent data (last 100 periods)
            recent_data = data.tail(100)
            current_price = float(recent_data['Close'].iloc[-1])
            
            # Find pivot points
            highs = recent_data['High'].values
            lows = recent_data['Low'].values
            
            # Resistance levels (recent highs)
            resistance_levels = []
            for i in range(2, len(highs) - 2):
                if highs[i] > highs[i-1] and highs[i] > highs[i-2] and \
                   highs[i] > highs[i+1] and highs[i] > highs[i+2]:
                    resistance_levels.append(highs[i])
            
            # Support levels (recent lows)
            support_levels = []
            for i in range(2, len(lows) - 2):
                if lows[i] < lows[i-1] and lows[i] < lows[i-2] and \
                   lows[i] < lows[i+1] and lows[i] < lows[i+2]:
                    support_levels.append(lows[i])
            
            # Filter and sort levels
            resistance_levels = sorted([r for r in resistance_levels if r > current_price])[:3]
            support_levels = sorted([s for s in support_levels if s < current_price], reverse=True)[:3]
            
            return {
                'current_price': current_price,
                'resistance_levels': resistance_levels,
                'support_levels': support_levels,
                'nearest_resistance': resistance_levels[0] if resistance_levels else None,
                'nearest_support': support_levels[0] if support_levels else None
            }
            
        except Exception as e:
            logger.error(f"Error finding support/resistance: {e}")
            return {'error': str(e)}
    
    async def _recognize_patterns(self, data: pd.DataFrame) -> Dict:
        """Recognize chart patterns"""
        try:
            patterns = []
            
            if len(data) < 20:
                return {'patterns': patterns}
            
            recent = data.tail(20)
            closes = recent['Close'].values
            
            # Simple pattern recognition
            # Double top/bottom
            if len(closes) >= 10:
                mid_point = len(closes) // 2
                first_half_max = np.max(closes[:mid_point])
                second_half_max = np.max(closes[mid_point:])
                
                if abs(first_half_max - second_half_max) / first_half_max < 0.02:
                    patterns.append("Potential Double Top")
            
            # Trend patterns
            if closes[-1] > closes[-5] > closes[-10]:
                patterns.append("Ascending Trend")
            elif closes[-1] < closes[-5] < closes[-10]:
                patterns.append("Descending Trend")
            
            # Consolidation
            price_range = (np.max(closes) - np.min(closes)) / np.mean(closes)
            if price_range < 0.02:
                patterns.append("Consolidation")
            
            return {'patterns': patterns}
            
        except Exception as e:
            logger.error(f"Error in pattern recognition: {e}")
            return {'patterns': []}
    
    async def _analyze_market_sentiment(self, data: pd.DataFrame) -> Dict:
        """Analyze market sentiment"""
        try:
            sentiment_score = 0
            factors = []
            
            # Volume analysis
            if 'Volume' in data.columns and 'Volume_SMA' in data.columns:
                latest_volume_ratio = data['Volume_Ratio'].iloc[-1] if 'Volume_Ratio' in data.columns else 1
                if latest_volume_ratio > 1.5:
                    sentiment_score += 1
                    factors.append("High Volume")
                elif latest_volume_ratio < 0.5:
                    sentiment_score -= 1
                    factors.append("Low Volume")
            
            # Price momentum
            if len(data) >= 5:
                recent_change = (data['Close'].iloc[-1] - data['Close'].iloc[-5]) / data['Close'].iloc[-5]
                if recent_change > 0.02:
                    sentiment_score += 1
                    factors.append("Positive Momentum")
                elif recent_change < -0.02:
                    sentiment_score -= 1
                    factors.append("Negative Momentum")
            
            # VIX analysis (if available)
            if 'VIX' in data.columns:
                latest_vix = data['VIX'].iloc[-1]
                if latest_vix > 25:
                    sentiment_score -= 1
                    factors.append("High Fear (VIX)")
                elif latest_vix < 15:
                    sentiment_score += 1
                    factors.append("Low Fear (VIX)")
            
            # Normalize sentiment
            sentiment_score = max(-3, min(3, sentiment_score))
            
            if sentiment_score > 1:
                sentiment = "Bullish"
            elif sentiment_score < -1:
                sentiment = "Bearish"
            else:
                sentiment = "Neutral"
            
            return {
                'sentiment': sentiment,
                'sentiment_score': sentiment_score,
                'factors': factors
            }
            
        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return {'sentiment': 'Neutral', 'sentiment_score': 0, 'factors': []}
    
    async def _analyze_volatility(self, data: pd.DataFrame) -> Dict:
        """Analyze market volatility"""
        try:
            if 'ATR' not in data.columns:
                return {'error': 'ATR data not available'}
            
            current_atr = float(data['ATR'].iloc[-1])
            avg_atr = float(data['ATR'].tail(20).mean())
            current_price = float(data['Close'].iloc[-1])
            
            volatility_ratio = current_atr / avg_atr
            volatility_pct = (current_atr / current_price) * 100
            
            if volatility_ratio > 1.5:
                volatility_level = "High"
            elif volatility_ratio < 0.7:
                volatility_level = "Low"
            else:
                volatility_level = "Normal"
            
            return {
                'volatility_level': volatility_level,
                'current_atr': current_atr,
                'avg_atr': avg_atr,
                'volatility_ratio': volatility_ratio,
                'volatility_pct': volatility_pct
            }
            
        except Exception as e:
            logger.error(f"Error in volatility analysis: {e}")
            return {'error': str(e)}
    
    async def _analyze_momentum(self, data: pd.DataFrame) -> Dict:
        """Analyze price momentum"""
        try:
            momentum_signals = []
            
            # MACD momentum
            if 'MACD_Histogram' in data.columns:
                macd_hist = data['MACD_Histogram'].tail(3)
                if macd_hist.iloc[-1] > macd_hist.iloc[-2] > macd_hist.iloc[-3]:
                    momentum_signals.append("MACD Accelerating")
                elif macd_hist.iloc[-1] < macd_hist.iloc[-2] < macd_hist.iloc[-3]:
                    momentum_signals.append("MACD Decelerating")
            
            # RSI momentum
            if 'RSI' in data.columns:
                rsi_current = data['RSI'].iloc[-1]
                rsi_prev = data['RSI'].iloc[-2] if len(data) > 1 else rsi_current
                
                if rsi_current > rsi_prev and rsi_current > 50:
                    momentum_signals.append("RSI Bullish Momentum")
                elif rsi_current < rsi_prev and rsi_current < 50:
                    momentum_signals.append("RSI Bearish Momentum")
            
            # Price momentum
            if 'Momentum_10' in data.columns:
                momentum_10 = data['Momentum_10'].iloc[-1]
                if momentum_10 > 0.02:
                    momentum_signals.append("Strong Bullish Momentum")
                elif momentum_10 < -0.02:
                    momentum_signals.append("Strong Bearish Momentum")
            
            return {'momentum_signals': momentum_signals}
            
        except Exception as e:
            logger.error(f"Error in momentum analysis: {e}")
            return {'momentum_signals': []}
    
    async def _analyze_volume(self, data: pd.DataFrame) -> Dict:
        """Analyze volume patterns"""
        try:
            if 'Volume' not in data.columns:
                return {'error': 'Volume data not available'}
            
            volume_signals = []
            current_volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].tail(20).mean()
            
            volume_ratio = current_volume / avg_volume
            
            if volume_ratio > 2:
                volume_signals.append("Extremely High Volume")
            elif volume_ratio > 1.5:
                volume_signals.append("High Volume")
            elif volume_ratio < 0.5:
                volume_signals.append("Low Volume")
            
            # Volume trend
            recent_volumes = data['Volume'].tail(5)
            if recent_volumes.is_monotonic_increasing:
                volume_signals.append("Increasing Volume Trend")
            elif recent_volumes.is_monotonic_decreasing:
                volume_signals.append("Decreasing Volume Trend")
            
            return {
                'volume_signals': volume_signals,
                'current_volume': float(current_volume),
                'avg_volume': float(avg_volume),
                'volume_ratio': float(volume_ratio)
            }
            
        except Exception as e:
            logger.error(f"Error in volume analysis: {e}")
            return {'error': str(e)}
    
    def _calculate_confidence(self, results: Dict) -> float:
        """Calculate overall confidence score"""
        try:
            confidence_factors = []
            
            # Price prediction confidence
            if 'price_prediction' in results and 'confidence' in results['price_prediction']:
                confidence_factors.append(results['price_prediction']['confidence'])
            
            # Trend analysis confidence
            if 'trend_analysis' in results and 'trend_strength' in results['trend_analysis']:
                confidence_factors.append(results['trend_analysis']['trend_strength'])
            
            # Pattern recognition confidence
            if 'pattern_recognition' in results:
                pattern_count = len(results['pattern_recognition'].get('patterns', []))
                confidence_factors.append(min(pattern_count / 3, 1.0))
            
            # Volume confirmation
            if 'volume_analysis' in results:
                volume_signals = len(results['volume_analysis'].get('volume_signals', []))
                confidence_factors.append(min(volume_signals / 3, 1.0))
            
            if confidence_factors:
                return float(np.mean(confidence_factors))
            else:
                return 0.5
                
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5
    
    def _get_fallback_analysis(self) -> Dict:
        """Get fallback analysis when main analysis fails"""
        return {
            'price_prediction': {'error': 'Analysis failed'},
            'trend_analysis': {'overall_trend': 'Unknown', 'trend_strength': 0},
            'support_resistance': {'error': 'Analysis failed'},
            'pattern_recognition': {'patterns': []},
            'sentiment_analysis': {'sentiment': 'Neutral', 'sentiment_score': 0, 'factors': []},
            'volatility_analysis': {'volatility_level': 'Unknown'},
            'momentum_analysis': {'momentum_signals': []},
            'volume_analysis': {'volume_signals': []},
            'confidence_score': 0.1,
            'timestamp': datetime.now().isoformat()
        }
