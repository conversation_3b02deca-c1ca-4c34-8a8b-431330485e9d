"""
📊 Signal Generator Module
Advanced trading signal generation with multiple strategies
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum

logger = logging.getLogger(__name__)

class SignalType(Enum):
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    STRONG_BUY = "STRONG_BUY"
    STRONG_SELL = "STRONG_SELL"

class SignalStrength(Enum):
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    VERY_STRONG = 4
    EXTREME = 5

class SignalGenerator:
    """Advanced signal generator with multiple strategies"""
    
    def __init__(self):
        self.strategies = {
            'trend_following': self._trend_following_strategy,
            'mean_reversion': self._mean_reversion_strategy,
            'momentum': self._momentum_strategy,
            'breakout': self._breakout_strategy,
            'volume_analysis': self._volume_strategy,
            'multi_timeframe': self._multi_timeframe_strategy
        }
        
        self.signal_weights = {
            'trend_following': 0.25,
            'mean_reversion': 0.15,
            'momentum': 0.20,
            'breakout': 0.20,
            'volume_analysis': 0.10,
            'multi_timeframe': 0.10
        }
    
    async def generate_signals(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Generate comprehensive trading signals"""
        try:
            logger.info("📊 Generating trading signals...")
            
            if len(data) < 50:
                return {'error': 'Insufficient data for signal generation'}
            
            # Generate signals from all strategies
            strategy_signals = {}
            
            for strategy_name, strategy_func in self.strategies.items():
                try:
                    signal = await strategy_func(data, analysis)
                    strategy_signals[strategy_name] = signal
                except Exception as e:
                    logger.warning(f"Strategy {strategy_name} failed: {e}")
                    strategy_signals[strategy_name] = {
                        'signal': SignalType.HOLD,
                        'strength': SignalStrength.WEAK,
                        'confidence': 0.0
                    }
            
            # Combine signals
            combined_signal = self._combine_signals(strategy_signals)
            
            # Generate entry/exit points
            entry_exit = await self._calculate_entry_exit_points(data, combined_signal, analysis)
            
            # Generate stop loss and take profit
            risk_levels = await self._calculate_risk_levels(data, combined_signal, analysis)
            
            result = {
                'main_signal': combined_signal,
                'strategy_signals': strategy_signals,
                'entry_exit': entry_exit,
                'risk_levels': risk_levels,
                'market_conditions': self._assess_market_conditions(data, analysis),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("✅ Trading signals generated successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error generating signals: {e}")
            return {'error': str(e)}
    
    async def _trend_following_strategy(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Trend following strategy"""
        try:
            latest = data.iloc[-1]
            signal = SignalType.HOLD
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasons = []
            
            # Moving average analysis
            if all(col in data.columns for col in ['SMA_20', 'SMA_50', 'SMA_200']):
                close = latest['Close']
                sma_20 = latest['SMA_20']
                sma_50 = latest['SMA_50']
                sma_200 = latest['SMA_200']
                
                # Golden cross / Death cross
                if sma_20 > sma_50 > sma_200 and close > sma_20:
                    signal = SignalType.BUY
                    strength = SignalStrength.STRONG
                    confidence = 0.8
                    reasons.append("Golden Cross Formation")
                    
                elif sma_20 < sma_50 < sma_200 and close < sma_20:
                    signal = SignalType.SELL
                    strength = SignalStrength.STRONG
                    confidence = 0.8
                    reasons.append("Death Cross Formation")
                    
                elif close > sma_20 > sma_50:
                    signal = SignalType.BUY
                    strength = SignalStrength.MODERATE
                    confidence = 0.6
                    reasons.append("Above Short-term MAs")
                    
                elif close < sma_20 < sma_50:
                    signal = SignalType.SELL
                    strength = SignalStrength.MODERATE
                    confidence = 0.6
                    reasons.append("Below Short-term MAs")
            
            # MACD confirmation
            if all(col in data.columns for col in ['MACD', 'MACD_Signal']):
                macd = latest['MACD']
                macd_signal = latest['MACD_Signal']
                
                if macd > macd_signal and signal == SignalType.BUY:
                    confidence += 0.1
                    reasons.append("MACD Bullish")
                elif macd < macd_signal and signal == SignalType.SELL:
                    confidence += 0.1
                    reasons.append("MACD Bearish")
            
            return {
                'signal': signal,
                'strength': strength,
                'confidence': min(confidence, 1.0),
                'reasons': reasons
            }
            
        except Exception as e:
            logger.error(f"Error in trend following strategy: {e}")
            return {'signal': SignalType.HOLD, 'strength': SignalStrength.WEAK, 'confidence': 0.0, 'reasons': []}
    
    async def _mean_reversion_strategy(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Mean reversion strategy"""
        try:
            latest = data.iloc[-1]
            signal = SignalType.HOLD
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasons = []
            
            # RSI analysis
            if 'RSI' in data.columns:
                rsi = latest['RSI']
                
                if rsi < 30:
                    signal = SignalType.BUY
                    strength = SignalStrength.STRONG
                    confidence = 0.7
                    reasons.append(f"RSI Oversold ({rsi:.1f})")
                    
                elif rsi > 70:
                    signal = SignalType.SELL
                    strength = SignalStrength.STRONG
                    confidence = 0.7
                    reasons.append(f"RSI Overbought ({rsi:.1f})")
                    
                elif rsi < 40:
                    signal = SignalType.BUY
                    strength = SignalStrength.MODERATE
                    confidence = 0.5
                    reasons.append(f"RSI Low ({rsi:.1f})")
                    
                elif rsi > 60:
                    signal = SignalType.SELL
                    strength = SignalStrength.MODERATE
                    confidence = 0.5
                    reasons.append(f"RSI High ({rsi:.1f})")
            
            # Bollinger Bands analysis
            if all(col in data.columns for col in ['BB_Upper', 'BB_Lower', 'BB_Position']):
                bb_position = latest['BB_Position']
                close = latest['Close']
                bb_upper = latest['BB_Upper']
                bb_lower = latest['BB_Lower']
                
                if close <= bb_lower:
                    signal = SignalType.BUY
                    strength = SignalStrength.STRONG
                    confidence = 0.8
                    reasons.append("Price at Lower Bollinger Band")
                    
                elif close >= bb_upper:
                    signal = SignalType.SELL
                    strength = SignalStrength.STRONG
                    confidence = 0.8
                    reasons.append("Price at Upper Bollinger Band")
                    
                elif bb_position < 0.2:
                    signal = SignalType.BUY
                    strength = SignalStrength.MODERATE
                    confidence = 0.6
                    reasons.append("Near Lower BB")
                    
                elif bb_position > 0.8:
                    signal = SignalType.SELL
                    strength = SignalStrength.MODERATE
                    confidence = 0.6
                    reasons.append("Near Upper BB")
            
            return {
                'signal': signal,
                'strength': strength,
                'confidence': confidence,
                'reasons': reasons
            }
            
        except Exception as e:
            logger.error(f"Error in mean reversion strategy: {e}")
            return {'signal': SignalType.HOLD, 'strength': SignalStrength.WEAK, 'confidence': 0.0, 'reasons': []}
    
    async def _momentum_strategy(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Momentum strategy"""
        try:
            signal = SignalType.HOLD
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasons = []
            
            # Price momentum
            if len(data) >= 10:
                recent_change = (data['Close'].iloc[-1] - data['Close'].iloc[-10]) / data['Close'].iloc[-10]
                
                if recent_change > 0.03:
                    signal = SignalType.BUY
                    strength = SignalStrength.STRONG
                    confidence = 0.7
                    reasons.append(f"Strong Bullish Momentum ({recent_change*100:.1f}%)")
                    
                elif recent_change < -0.03:
                    signal = SignalType.SELL
                    strength = SignalStrength.STRONG
                    confidence = 0.7
                    reasons.append(f"Strong Bearish Momentum ({recent_change*100:.1f}%)")
                    
                elif recent_change > 0.01:
                    signal = SignalType.BUY
                    strength = SignalStrength.MODERATE
                    confidence = 0.5
                    reasons.append(f"Bullish Momentum ({recent_change*100:.1f}%)")
                    
                elif recent_change < -0.01:
                    signal = SignalType.SELL
                    strength = SignalStrength.MODERATE
                    confidence = 0.5
                    reasons.append(f"Bearish Momentum ({recent_change*100:.1f}%)")
            
            # MACD momentum
            if 'MACD_Histogram' in data.columns and len(data) >= 3:
                macd_hist = data['MACD_Histogram'].tail(3)
                if all(macd_hist.iloc[i] > macd_hist.iloc[i-1] for i in range(1, len(macd_hist))):
                    if signal == SignalType.BUY:
                        confidence += 0.1
                    reasons.append("MACD Momentum Accelerating")
                elif all(macd_hist.iloc[i] < macd_hist.iloc[i-1] for i in range(1, len(macd_hist))):
                    if signal == SignalType.SELL:
                        confidence += 0.1
                    reasons.append("MACD Momentum Decelerating")
            
            return {
                'signal': signal,
                'strength': strength,
                'confidence': min(confidence, 1.0),
                'reasons': reasons
            }
            
        except Exception as e:
            logger.error(f"Error in momentum strategy: {e}")
            return {'signal': SignalType.HOLD, 'strength': SignalStrength.WEAK, 'confidence': 0.0, 'reasons': []}
    
    async def _breakout_strategy(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Breakout strategy"""
        try:
            signal = SignalType.HOLD
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasons = []
            
            # Support/Resistance breakout
            if 'support_resistance' in analysis:
                sr_data = analysis['support_resistance']
                current_price = sr_data.get('current_price', 0)
                resistance_levels = sr_data.get('resistance_levels', [])
                support_levels = sr_data.get('support_levels', [])
                
                # Resistance breakout
                if resistance_levels:
                    nearest_resistance = min(resistance_levels)
                    if current_price > nearest_resistance * 1.002:  # 0.2% above resistance
                        signal = SignalType.BUY
                        strength = SignalStrength.STRONG
                        confidence = 0.8
                        reasons.append(f"Resistance Breakout at {nearest_resistance:.2f}")
                
                # Support breakdown
                if support_levels:
                    nearest_support = max(support_levels)
                    if current_price < nearest_support * 0.998:  # 0.2% below support
                        signal = SignalType.SELL
                        strength = SignalStrength.STRONG
                        confidence = 0.8
                        reasons.append(f"Support Breakdown at {nearest_support:.2f}")
            
            # Bollinger Band breakout
            if all(col in data.columns for col in ['BB_Upper', 'BB_Lower', 'BB_Width']):
                latest = data.iloc[-1]
                close = latest['Close']
                bb_upper = latest['BB_Upper']
                bb_lower = latest['BB_Lower']
                bb_width = latest['BB_Width']
                
                # Squeeze breakout (low volatility followed by breakout)
                avg_bb_width = data['BB_Width'].tail(20).mean()
                if bb_width < avg_bb_width * 0.8:  # Squeeze condition
                    if close > bb_upper:
                        signal = SignalType.BUY
                        strength = SignalStrength.VERY_STRONG
                        confidence = 0.9
                        reasons.append("Bollinger Band Squeeze Breakout (Bullish)")
                    elif close < bb_lower:
                        signal = SignalType.SELL
                        strength = SignalStrength.VERY_STRONG
                        confidence = 0.9
                        reasons.append("Bollinger Band Squeeze Breakout (Bearish)")
            
            return {
                'signal': signal,
                'strength': strength,
                'confidence': confidence,
                'reasons': reasons
            }
            
        except Exception as e:
            logger.error(f"Error in breakout strategy: {e}")
            return {'signal': SignalType.HOLD, 'strength': SignalStrength.WEAK, 'confidence': 0.0, 'reasons': []}
    
    async def _volume_strategy(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Volume-based strategy"""
        try:
            signal = SignalType.HOLD
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasons = []
            
            if 'Volume' not in data.columns:
                return {'signal': signal, 'strength': strength, 'confidence': confidence, 'reasons': reasons}
            
            latest = data.iloc[-1]
            current_volume = latest['Volume']
            avg_volume = data['Volume'].tail(20).mean()
            volume_ratio = current_volume / avg_volume
            
            # High volume confirmation
            if volume_ratio > 2:
                # Check price direction
                if len(data) >= 2:
                    price_change = (latest['Close'] - data['Close'].iloc[-2]) / data['Close'].iloc[-2]
                    
                    if price_change > 0.005:  # 0.5% up with high volume
                        signal = SignalType.BUY
                        strength = SignalStrength.STRONG
                        confidence = 0.7
                        reasons.append(f"High Volume Bullish ({volume_ratio:.1f}x avg)")
                        
                    elif price_change < -0.005:  # 0.5% down with high volume
                        signal = SignalType.SELL
                        strength = SignalStrength.STRONG
                        confidence = 0.7
                        reasons.append(f"High Volume Bearish ({volume_ratio:.1f}x avg)")
            
            # Volume trend analysis
            if len(data) >= 5:
                recent_volumes = data['Volume'].tail(5)
                if recent_volumes.is_monotonic_increasing:
                    confidence += 0.1
                    reasons.append("Increasing Volume Trend")
            
            return {
                'signal': signal,
                'strength': strength,
                'confidence': min(confidence, 1.0),
                'reasons': reasons
            }
            
        except Exception as e:
            logger.error(f"Error in volume strategy: {e}")
            return {'signal': SignalType.HOLD, 'strength': SignalStrength.WEAK, 'confidence': 0.0, 'reasons': []}
    
    async def _multi_timeframe_strategy(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Multi-timeframe analysis strategy"""
        try:
            signal = SignalType.HOLD
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasons = []
            
            # This would ideally use multiple timeframe data
            # For now, we'll use trend analysis from the main analysis
            if 'trend_analysis' in analysis:
                trend_data = analysis['trend_analysis']
                overall_trend = trend_data.get('overall_trend', 'Neutral')
                trend_strength = trend_data.get('trend_strength', 0)
                
                if overall_trend == 'Bullish' and trend_strength > 0.7:
                    signal = SignalType.BUY
                    strength = SignalStrength.STRONG
                    confidence = trend_strength
                    reasons.append("Strong Multi-timeframe Bullish Trend")
                    
                elif overall_trend == 'Bearish' and trend_strength > 0.7:
                    signal = SignalType.SELL
                    strength = SignalStrength.STRONG
                    confidence = trend_strength
                    reasons.append("Strong Multi-timeframe Bearish Trend")
                    
                elif overall_trend == 'Bullish':
                    signal = SignalType.BUY
                    strength = SignalStrength.MODERATE
                    confidence = trend_strength
                    reasons.append("Multi-timeframe Bullish Trend")
                    
                elif overall_trend == 'Bearish':
                    signal = SignalType.SELL
                    strength = SignalStrength.MODERATE
                    confidence = trend_strength
                    reasons.append("Multi-timeframe Bearish Trend")
            
            return {
                'signal': signal,
                'strength': strength,
                'confidence': confidence,
                'reasons': reasons
            }
            
        except Exception as e:
            logger.error(f"Error in multi-timeframe strategy: {e}")
            return {'signal': SignalType.HOLD, 'strength': SignalStrength.WEAK, 'confidence': 0.0, 'reasons': []}
    
    def _combine_signals(self, strategy_signals: Dict) -> Dict:
        """Combine signals from all strategies"""
        try:
            # Calculate weighted signal score
            signal_scores = {
                SignalType.STRONG_SELL: -2,
                SignalType.SELL: -1,
                SignalType.HOLD: 0,
                SignalType.BUY: 1,
                SignalType.STRONG_BUY: 2
            }
            
            weighted_score = 0
            total_confidence = 0
            all_reasons = []
            
            for strategy_name, signal_data in strategy_signals.items():
                if 'signal' in signal_data and 'confidence' in signal_data:
                    signal = signal_data['signal']
                    confidence = signal_data['confidence']
                    weight = self.signal_weights.get(strategy_name, 0.1)
                    
                    score = signal_scores.get(signal, 0)
                    weighted_score += score * confidence * weight
                    total_confidence += confidence * weight
                    
                    if signal_data.get('reasons'):
                        all_reasons.extend([f"{strategy_name}: {reason}" for reason in signal_data['reasons']])
            
            # Determine final signal
            if weighted_score > 0.6:
                final_signal = SignalType.STRONG_BUY
                final_strength = SignalStrength.VERY_STRONG
            elif weighted_score > 0.3:
                final_signal = SignalType.BUY
                final_strength = SignalStrength.STRONG
            elif weighted_score < -0.6:
                final_signal = SignalType.STRONG_SELL
                final_strength = SignalStrength.VERY_STRONG
            elif weighted_score < -0.3:
                final_signal = SignalType.SELL
                final_strength = SignalStrength.STRONG
            else:
                final_signal = SignalType.HOLD
                final_strength = SignalStrength.MODERATE
            
            return {
                'signal': final_signal,
                'strength': final_strength,
                'confidence': min(total_confidence, 1.0),
                'score': weighted_score,
                'reasons': all_reasons[:10]  # Limit to top 10 reasons
            }
            
        except Exception as e:
            logger.error(f"Error combining signals: {e}")
            return {
                'signal': SignalType.HOLD,
                'strength': SignalStrength.WEAK,
                'confidence': 0.0,
                'score': 0.0,
                'reasons': []
            }
    
    async def _calculate_entry_exit_points(self, data: pd.DataFrame, signal: Dict, analysis: Dict) -> Dict:
        """Calculate optimal entry and exit points"""
        try:
            current_price = float(data['Close'].iloc[-1])
            signal_type = signal.get('signal', SignalType.HOLD)
            
            entry_points = {}
            exit_points = {}
            
            if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                # Entry points for buy signals
                entry_points = {
                    'immediate': current_price,
                    'pullback_5%': current_price * 0.95,
                    'pullback_10%': current_price * 0.90,
                    'support_level': None
                }
                
                # Add support level if available
                if 'support_resistance' in analysis:
                    support_levels = analysis['support_resistance'].get('support_levels', [])
                    if support_levels:
                        entry_points['support_level'] = max(support_levels)
                
                # Exit points for buy signals
                exit_points = {
                    'target_5%': current_price * 1.05,
                    'target_10%': current_price * 1.10,
                    'target_15%': current_price * 1.15,
                    'resistance_level': None
                }
                
                # Add resistance level if available
                if 'support_resistance' in analysis:
                    resistance_levels = analysis['support_resistance'].get('resistance_levels', [])
                    if resistance_levels:
                        exit_points['resistance_level'] = min(resistance_levels)
            
            elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                # Entry points for sell signals
                entry_points = {
                    'immediate': current_price,
                    'bounce_5%': current_price * 1.05,
                    'bounce_10%': current_price * 1.10,
                    'resistance_level': None
                }
                
                # Add resistance level if available
                if 'support_resistance' in analysis:
                    resistance_levels = analysis['support_resistance'].get('resistance_levels', [])
                    if resistance_levels:
                        entry_points['resistance_level'] = min(resistance_levels)
                
                # Exit points for sell signals
                exit_points = {
                    'target_5%': current_price * 0.95,
                    'target_10%': current_price * 0.90,
                    'target_15%': current_price * 0.85,
                    'support_level': None
                }
                
                # Add support level if available
                if 'support_resistance' in analysis:
                    support_levels = analysis['support_resistance'].get('support_levels', [])
                    if support_levels:
                        exit_points['support_level'] = max(support_levels)
            
            return {
                'current_price': current_price,
                'entry_points': entry_points,
                'exit_points': exit_points,
                'recommended_entry': entry_points.get('immediate', current_price),
                'recommended_exit': list(exit_points.values())[0] if exit_points else current_price
            }
            
        except Exception as e:
            logger.error(f"Error calculating entry/exit points: {e}")
            return {'error': str(e)}
    
    async def _calculate_risk_levels(self, data: pd.DataFrame, signal: Dict, analysis: Dict) -> Dict:
        """Calculate stop loss and take profit levels"""
        try:
            current_price = float(data['Close'].iloc[-1])
            signal_type = signal.get('signal', SignalType.HOLD)
            
            # Get ATR for dynamic stop loss
            atr = float(data['ATR'].iloc[-1]) if 'ATR' in data.columns else current_price * 0.02
            
            risk_levels = {}
            
            if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                # Stop loss levels for buy signals
                risk_levels['stop_loss'] = {
                    'tight': current_price - (atr * 1.5),
                    'normal': current_price - (atr * 2.0),
                    'wide': current_price - (atr * 3.0),
                    'percentage_2%': current_price * 0.98,
                    'percentage_5%': current_price * 0.95
                }
                
                # Take profit levels for buy signals
                risk_levels['take_profit'] = {
                    'conservative': current_price + (atr * 2.0),
                    'moderate': current_price + (atr * 3.0),
                    'aggressive': current_price + (atr * 4.0),
                    'percentage_5%': current_price * 1.05,
                    'percentage_10%': current_price * 1.10
                }
                
                # Recommended levels (2:1 risk-reward ratio)
                recommended_sl = current_price - (atr * 2.0)
                recommended_tp = current_price + (atr * 4.0)
                
            elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                # Stop loss levels for sell signals
                risk_levels['stop_loss'] = {
                    'tight': current_price + (atr * 1.5),
                    'normal': current_price + (atr * 2.0),
                    'wide': current_price + (atr * 3.0),
                    'percentage_2%': current_price * 1.02,
                    'percentage_5%': current_price * 1.05
                }
                
                # Take profit levels for sell signals
                risk_levels['take_profit'] = {
                    'conservative': current_price - (atr * 2.0),
                    'moderate': current_price - (atr * 3.0),
                    'aggressive': current_price - (atr * 4.0),
                    'percentage_5%': current_price * 0.95,
                    'percentage_10%': current_price * 0.90
                }
                
                # Recommended levels (2:1 risk-reward ratio)
                recommended_sl = current_price + (atr * 2.0)
                recommended_tp = current_price - (atr * 4.0)
                
            else:
                # No position
                return {'message': 'No position recommended'}
            
            risk_levels['recommended'] = {
                'stop_loss': recommended_sl,
                'take_profit': recommended_tp,
                'risk_reward_ratio': 2.0,
                'position_size_suggestion': '2-3% of portfolio'
            }
            
            return risk_levels
            
        except Exception as e:
            logger.error(f"Error calculating risk levels: {e}")
            return {'error': str(e)}
    
    def _assess_market_conditions(self, data: pd.DataFrame, analysis: Dict) -> Dict:
        """Assess current market conditions"""
        try:
            conditions = {
                'volatility': 'Normal',
                'trend': 'Neutral',
                'volume': 'Normal',
                'sentiment': 'Neutral',
                'overall_rating': 'Neutral'
            }
            
            # Volatility assessment
            if 'volatility_analysis' in analysis:
                vol_data = analysis['volatility_analysis']
                conditions['volatility'] = vol_data.get('volatility_level', 'Normal')
            
            # Trend assessment
            if 'trend_analysis' in analysis:
                trend_data = analysis['trend_analysis']
                conditions['trend'] = trend_data.get('overall_trend', 'Neutral')
            
            # Volume assessment
            if 'volume_analysis' in analysis:
                vol_signals = analysis['volume_analysis'].get('volume_signals', [])
                if any('High' in signal for signal in vol_signals):
                    conditions['volume'] = 'High'
                elif any('Low' in signal for signal in vol_signals):
                    conditions['volume'] = 'Low'
            
            # Sentiment assessment
            if 'sentiment_analysis' in analysis:
                sentiment_data = analysis['sentiment_analysis']
                conditions['sentiment'] = sentiment_data.get('sentiment', 'Neutral')
            
            # Overall rating
            positive_factors = sum([
                conditions['trend'] in ['Bullish', 'Strong Bullish'],
                conditions['sentiment'] in ['Bullish'],
                conditions['volume'] == 'High' and conditions['trend'] != 'Bearish'
            ])
            
            negative_factors = sum([
                conditions['trend'] in ['Bearish', 'Strong Bearish'],
                conditions['sentiment'] in ['Bearish'],
                conditions['volatility'] == 'High'
            ])
            
            if positive_factors > negative_factors:
                conditions['overall_rating'] = 'Favorable'
            elif negative_factors > positive_factors:
                conditions['overall_rating'] = 'Unfavorable'
            else:
                conditions['overall_rating'] = 'Neutral'
            
            return conditions
            
        except Exception as e:
            logger.error(f"Error assessing market conditions: {e}")
            return {
                'volatility': 'Unknown',
                'trend': 'Unknown',
                'volume': 'Unknown',
                'sentiment': 'Unknown',
                'overall_rating': 'Unknown'
            }
