#!/usr/bin/env python3
"""
🚀 Gold Trading AI System Launcher
Quick start script for the Gold Trading AI System
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print system banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║               🥇 GOLD TRADING AI SYSTEM 🥇                   ║
    ║                                                              ║
    ║           Advanced AI-Powered Trading Analysis               ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    else:
        print(f"✅ Python version: {sys.version.split()[0]}")

def check_dependencies():
    """Check if required dependencies are installed"""
    print("\n🔍 Checking dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pandas',
        'numpy',
        'yfinance',
        'scikit-learn',
        'tensorflow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run: pip install -r requirements.txt")
            sys.exit(1)
    else:
        print("✅ All dependencies are installed")

def setup_environment():
    """Setup environment variables"""
    print("\n⚙️  Setting up environment...")
    
    # Set default environment if not specified
    if not os.getenv('ENVIRONMENT'):
        os.environ['ENVIRONMENT'] = 'development'
        print("✅ Environment set to: development")
    
    # Set default host and port if not specified
    if not os.getenv('HOST'):
        os.environ['HOST'] = '0.0.0.0'
    
    if not os.getenv('PORT'):
        os.environ['PORT'] = '8000'
    
    print(f"✅ Host: {os.getenv('HOST')}")
    print(f"✅ Port: {os.getenv('PORT')}")

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        'logs',
        'data',
        'models',
        'static',
        'templates'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}/")

def run_system():
    """Run the Gold Trading AI System"""
    print("\n🚀 Starting Gold Trading AI System...")
    print("=" * 60)
    
    try:
        # Import and run the main application
        from main import app
        import uvicorn
        
        host = os.getenv('HOST', '0.0.0.0')
        port = int(os.getenv('PORT', 8000))
        
        print(f"🌐 Dashboard URL: http://localhost:{port}")
        print(f"📊 API Documentation: http://localhost:{port}/docs")
        print("=" * 60)
        print("🔥 System is starting up...")
        print("⏳ Please wait for initialization to complete...")
        print("=" * 60)
        
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 System stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting system: {e}")
        sys.exit(1)

def main():
    """Main launcher function"""
    print_banner()
    
    print("🔧 System Initialization")
    print("=" * 30)
    
    # Check system requirements
    check_python_version()
    check_dependencies()
    setup_environment()
    create_directories()
    
    print("\n✅ System initialization complete!")
    print("🎯 Ready to launch Gold Trading AI System")
    
    # Wait a moment before starting
    time.sleep(2)
    
    # Run the system
    run_system()

if __name__ == "__main__":
    main()
