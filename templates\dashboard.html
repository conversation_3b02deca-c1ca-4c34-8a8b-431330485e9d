<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🥇 Gold Trading AI System</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #ffd700;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff00;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .price-display {
            grid-column: span 3;
            text-align: center;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            font-size: 3rem;
            font-weight: bold;
            padding: 2rem;
        }

        .price-change {
            font-size: 1.5rem;
            margin-top: 0.5rem;
        }

        .positive { color: #00ff00; }
        .negative { color: #ff4444; }

        .signal-card {
            text-align: center;
        }

        .signal-type {
            font-size: 2rem;
            font-weight: bold;
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 10px;
        }

        .signal-buy { background: linear-gradient(45deg, #00ff00, #32cd32); color: #000; }
        .signal-sell { background: linear-gradient(45deg, #ff4444, #ff6b6b); color: #fff; }
        .signal-hold { background: linear-gradient(45deg, #ffa500, #ffb347); color: #000; }

        .confidence-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444, #ffa500, #00ff00);
            transition: width 0.5s ease;
        }

        .chart-container {
            grid-column: span 2;
            height: 400px;
        }

        .risk-meter {
            text-align: center;
        }

        .risk-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 1rem auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            position: relative;
        }

        .risk-very-low { background: conic-gradient(#00ff00 0deg 72deg, rgba(255,255,255,0.2) 72deg 360deg); }
        .risk-low { background: conic-gradient(#32cd32 0deg 144deg, rgba(255,255,255,0.2) 144deg 360deg); }
        .risk-moderate { background: conic-gradient(#ffa500 0deg 216deg, rgba(255,255,255,0.2) 216deg 360deg); }
        .risk-high { background: conic-gradient(#ff6b6b 0deg 288deg, rgba(255,255,255,0.2) 288deg 360deg); }
        .risk-very-high { background: conic-gradient(#ff4444 0deg 360deg); }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        .metric {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffd700;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.5rem;
        }

        .alerts-container {
            grid-column: span 3;
            max-height: 200px;
            overflow-y: auto;
        }

        .alert {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .alert-info { border-left-color: #00bfff; }
        .alert-warning { border-left-color: #ffa500; }
        .alert-danger { border-left-color: #ff4444; }
        .alert-success { border-left-color: #00ff00; }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid #ffd700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .entry-exit-levels {
            grid-column: span 2;
        }

        .levels-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        .level-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.8rem;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .level-price {
            font-weight: bold;
            color: #ffd700;
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr 1fr;
            }
            .price-display, .chart-container, .entry-exit-levels, .alerts-container {
                grid-column: span 2;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                padding: 1rem;
            }
            .price-display, .chart-container, .entry-exit-levels, .alerts-container {
                grid-column: span 1;
            }
            .price-display {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <i class="fas fa-coins"></i> Gold Trading AI System
        </div>
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span id="connection-status">Connected</span>
        </div>
    </div>

    <div class="main-container">
        <!-- Current Price Display -->
        <div class="card price-display">
            <div id="current-price">Loading...</div>
            <div class="price-change" id="price-change">--</div>
            <div style="font-size: 1rem; margin-top: 1rem;" id="last-update">Last Update: --</div>
        </div>

        <!-- Trading Signal -->
        <div class="card signal-card">
            <div class="card-header">
                <i class="fas fa-signal"></i> Trading Signal
            </div>
            <div class="signal-type" id="signal-type">LOADING</div>
            <div class="confidence-bar">
                <div class="confidence-fill" id="confidence-fill" style="width: 0%"></div>
            </div>
            <div id="signal-confidence">Confidence: 0%</div>
        </div>

        <!-- Risk Assessment -->
        <div class="card risk-meter">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle"></i> Risk Level
            </div>
            <div class="risk-circle" id="risk-circle">
                <span id="risk-level">LOADING</span>
            </div>
            <div id="risk-description">Calculating...</div>
        </div>

        <!-- Price Chart -->
        <div class="card chart-container">
            <div class="card-header">
                <i class="fas fa-chart-line"></i> Price Chart
            </div>
            <canvas id="price-chart"></canvas>
        </div>

        <!-- Key Metrics -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tachometer-alt"></i> Key Metrics
            </div>
            <div class="metrics-grid">
                <div class="metric">
                    <div class="metric-value" id="rsi-value">--</div>
                    <div class="metric-label">RSI</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="macd-value">--</div>
                    <div class="metric-label">MACD</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="volume-ratio">--</div>
                    <div class="metric-label">Volume Ratio</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="atr-value">--</div>
                    <div class="metric-label">ATR</div>
                </div>
            </div>
        </div>

        <!-- Entry/Exit Levels -->
        <div class="card entry-exit-levels">
            <div class="card-header">
                <i class="fas fa-crosshairs"></i> Entry/Exit Levels
            </div>
            <div class="levels-grid">
                <div class="level-item">
                    <span>Entry Point:</span>
                    <span class="level-price" id="entry-point">--</span>
                </div>
                <div class="level-item">
                    <span>Stop Loss:</span>
                    <span class="level-price" id="stop-loss">--</span>
                </div>
                <div class="level-item">
                    <span>Take Profit:</span>
                    <span class="level-price" id="take-profit">--</span>
                </div>
                <div class="level-item">
                    <span>Risk/Reward:</span>
                    <span class="level-price" id="risk-reward">--</span>
                </div>
            </div>
        </div>

        <!-- Alerts and Notifications -->
        <div class="card alerts-container">
            <div class="card-header">
                <i class="fas fa-bell"></i> Alerts & Analysis
            </div>
            <div id="alerts-list">
                <div class="loading">
                    <div class="spinner"></div>
                    <span>Loading analysis...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection
        let ws = null;
        let priceChart = null;
        let priceData = [];
        let timeLabels = [];

        // Initialize the application
        function init() {
            connectWebSocket();
            initChart();
            loadInitialData();
        }

        // WebSocket connection
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                console.log('WebSocket connected');
                updateConnectionStatus(true);
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket disconnected');
                updateConnectionStatus(false);
                // Reconnect after 5 seconds
                setTimeout(connectWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                updateConnectionStatus(false);
            };
        }

        // Handle WebSocket messages
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'welcome':
                    console.log('Welcome message:', data.message);
                    break;
                case 'update':
                    updateQuickData(data.data);
                    break;
                case 'price_update':
                    updatePriceDisplay(data.data);
                    break;
                case 'signal_update':
                    updateSignalDisplay(data.data);
                    break;
                case 'analysis_update':
                    updateAnalysisDisplay(data.data);
                    break;
                case 'alert':
                    addAlert(data.alert_type, data.message, data.severity);
                    break;
            }
        }

        // Update connection status
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connection-status');
            const statusDot = document.querySelector('.status-dot');
            
            if (connected) {
                statusElement.textContent = 'Connected';
                statusDot.style.background = '#00ff00';
            } else {
                statusElement.textContent = 'Disconnected';
                statusDot.style.background = '#ff4444';
            }
        }

        // Initialize price chart
        function initChart() {
            const ctx = document.getElementById('price-chart').getContext('2d');
            priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        label: 'Gold Price',
                        data: priceData,
                        borderColor: '#ffd700',
                        backgroundColor: 'rgba(255, 215, 0, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // Load initial data
        async function loadInitialData() {
            try {
                // Load current price
                const priceResponse = await fetch('/api/gold/current');
                const priceData = await priceResponse.json();
                if (priceData.success) {
                    updatePriceDisplay(priceData.data);
                }

                // Load complete analysis
                const analysisResponse = await fetch('/api/analysis/complete');
                const analysisData = await analysisResponse.json();
                if (analysisData.success) {
                    updateCompleteAnalysis(analysisData.data);
                }
            } catch (error) {
                console.error('Error loading initial data:', error);
                addAlert('error', 'Failed to load initial data', 'danger');
            }
        }

        // Update price display
        function updatePriceDisplay(data) {
            const priceElement = document.getElementById('current-price');
            const changeElement = document.getElementById('price-change');
            const updateElement = document.getElementById('last-update');

            priceElement.textContent = `$${data.price.toFixed(2)}`;
            
            const change = data.daily_change;
            const changePct = data.daily_change_pct;
            
            changeElement.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePct.toFixed(2)}%)`;
            changeElement.className = `price-change ${change >= 0 ? 'positive' : 'negative'}`;
            
            updateElement.textContent = `Last Update: ${new Date(data.timestamp).toLocaleTimeString()}`;

            // Update chart
            updateChart(data.price);
        }

        // Update chart with new price
        function updateChart(price) {
            const now = new Date().toLocaleTimeString();
            
            priceData.push(price);
            timeLabels.push(now);
            
            // Keep only last 50 data points
            if (priceData.length > 50) {
                priceData.shift();
                timeLabels.shift();
            }
            
            priceChart.update();
        }

        // Update quick data from WebSocket
        function updateQuickData(data) {
            if (data.price) {
                updatePriceDisplay(data);
            }
            
            if (data.trend) {
                updateTrendDisplay(data);
            }
        }

        // Update trend display
        function updateTrendDisplay(data) {
            const signalElement = document.getElementById('signal-type');
            const confidenceElement = document.getElementById('signal-confidence');
            const confidenceFill = document.getElementById('confidence-fill');
            
            // Map trend to signal
            let signalClass = 'signal-hold';
            let signalText = data.trend;
            
            if (data.trend.includes('Bullish')) {
                signalClass = 'signal-buy';
                signalText = 'BUY';
            } else if (data.trend.includes('Bearish')) {
                signalClass = 'signal-sell';
                signalText = 'SELL';
            }
            
            signalElement.textContent = signalText;
            signalElement.className = `signal-type ${signalClass}`;
            
            const confidence = (data.trend_strength || 0.5) * 100;
            confidenceElement.textContent = `Confidence: ${confidence.toFixed(0)}%`;
            confidenceFill.style.width = `${confidence}%`;
        }

        // Update complete analysis
        function updateCompleteAnalysis(data) {
            // Update signals
            if (data.signals && data.signals.main_signal) {
                updateMainSignal(data.signals.main_signal);
            }
            
            // Update risk assessment
            if (data.risk && data.risk.overall_risk) {
                updateRiskDisplay(data.risk.overall_risk);
            }
            
            // Update entry/exit levels
            if (data.signals && data.signals.risk_levels) {
                updateEntryExitLevels(data.signals);
            }
            
            // Update key metrics
            if (data.analysis) {
                updateKeyMetrics(data.analysis);
            }
            
            // Update alerts
            updateAlerts(data);
        }

        // Update main signal display
        function updateMainSignal(signal) {
            const signalElement = document.getElementById('signal-type');
            const confidenceElement = document.getElementById('signal-confidence');
            const confidenceFill = document.getElementById('confidence-fill');
            
            let signalClass = 'signal-hold';
            let signalText = signal.signal || 'HOLD';
            
            if (signalText.includes('BUY')) {
                signalClass = 'signal-buy';
            } else if (signalText.includes('SELL')) {
                signalClass = 'signal-sell';
            }
            
            signalElement.textContent = signalText;
            signalElement.className = `signal-type ${signalClass}`;
            
            const confidence = (signal.confidence || 0) * 100;
            confidenceElement.textContent = `Confidence: ${confidence.toFixed(0)}%`;
            confidenceFill.style.width = `${confidence}%`;
        }

        // Update risk display
        function updateRiskDisplay(risk) {
            const riskElement = document.getElementById('risk-level');
            const riskCircle = document.getElementById('risk-circle');
            const riskDescription = document.getElementById('risk-description');
            
            const riskLevel = risk.risk_level || 'MODERATE';
            const riskClass = `risk-${riskLevel.toLowerCase().replace('_', '-')}`;
            
            riskElement.textContent = riskLevel.replace('_', ' ');
            riskCircle.className = `risk-circle ${riskClass}`;
            riskDescription.textContent = risk.description || 'Risk assessment';
        }

        // Update entry/exit levels
        function updateEntryExitLevels(signals) {
            const entryPoint = document.getElementById('entry-point');
            const stopLoss = document.getElementById('stop-loss');
            const takeProfit = document.getElementById('take-profit');
            const riskReward = document.getElementById('risk-reward');
            
            if (signals.entry_exit) {
                entryPoint.textContent = `$${(signals.entry_exit.recommended_entry || 0).toFixed(2)}`;
            }
            
            if (signals.risk_levels && signals.risk_levels.recommended) {
                const rec = signals.risk_levels.recommended;
                stopLoss.textContent = `$${(rec.stop_loss || 0).toFixed(2)}`;
                takeProfit.textContent = `$${(rec.take_profit || 0).toFixed(2)}`;
                riskReward.textContent = `1:${(rec.risk_reward_ratio || 1).toFixed(1)}`;
            }
        }

        // Update key metrics
        function updateKeyMetrics(analysis) {
            const rsiElement = document.getElementById('rsi-value');
            const macdElement = document.getElementById('macd-value');
            const volumeElement = document.getElementById('volume-ratio');
            const atrElement = document.getElementById('atr-value');
            
            if (analysis.trend_analysis) {
                rsiElement.textContent = (analysis.trend_analysis.rsi_value || 0).toFixed(1);
            }
            
            if (analysis.trend_analysis && analysis.trend_analysis.macd_trend) {
                macdElement.textContent = analysis.trend_analysis.macd_trend;
            }
            
            if (analysis.volume_analysis) {
                volumeElement.textContent = (analysis.volume_analysis.volume_ratio || 1).toFixed(2);
            }
            
            if (analysis.volatility_analysis) {
                atrElement.textContent = (analysis.volatility_analysis.current_atr || 0).toFixed(2);
            }
        }

        // Update alerts
        function updateAlerts(data) {
            const alertsList = document.getElementById('alerts-list');
            alertsList.innerHTML = '';
            
            // Add signal reasons
            if (data.signals && data.signals.main_signal && data.signals.main_signal.reasons) {
                data.signals.main_signal.reasons.forEach(reason => {
                    addAlert('signal', reason, 'info');
                });
            }
            
            // Add risk recommendations
            if (data.risk && data.risk.recommendations) {
                data.risk.recommendations.slice(0, 3).forEach(rec => {
                    addAlert('risk', rec, 'warning');
                });
            }
            
            // Add risk warnings
            if (data.risk && data.risk.risk_warnings) {
                data.risk.risk_warnings.slice(0, 2).forEach(warning => {
                    addAlert('warning', warning, 'danger');
                });
            }
        }

        // Add alert to the list
        function addAlert(type, message, severity = 'info') {
            const alertsList = document.getElementById('alerts-list');
            
            const alertElement = document.createElement('div');
            alertElement.className = `alert alert-${severity}`;
            
            const icon = getAlertIcon(severity);
            alertElement.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
            `;
            
            alertsList.appendChild(alertElement);
            
            // Remove old alerts if too many
            const alerts = alertsList.children;
            if (alerts.length > 10) {
                alertsList.removeChild(alerts[0]);
            }
        }

        // Get alert icon based on severity
        function getAlertIcon(severity) {
            switch(severity) {
                case 'success': return 'fas fa-check-circle';
                case 'warning': return 'fas fa-exclamation-triangle';
                case 'danger': return 'fas fa-exclamation-circle';
                default: return 'fas fa-info-circle';
            }
        }

        // Initialize the application when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
