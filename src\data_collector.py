"""
🔄 Data Collector Module
Advanced data collection for gold prices and market data
"""

import asyncio
import logging
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import requests
import json
import time

logger = logging.getLogger(__name__)

class GoldDataCollector:
    """Advanced gold data collector with multiple sources"""
    
    def __init__(self):
        self.symbols = {
            'gold_futures': 'GC=F',  # Gold Futures
            'gold_etf': 'GLD',       # SPDR Gold ETF
            'xauusd': 'XAUUSD=X',    # Gold/USD
            'dxy': 'DX-Y.NYB',       # Dollar Index
            'vix': '^VIX',           # Volatility Index
            'sp500': '^GSPC',        # S&P 500
            'bonds': '^TNX'          # 10-Year Treasury
        }
        self.cache = {}
        self.last_update = None
        
    async def get_current_price(self) -> Dict:
        """Get current gold price with real-time data"""
        try:
            # Add delay to avoid rate limiting
            await asyncio.sleep(1)

            # Get gold futures data
            gold_ticker = yf.Ticker(self.symbols['gold_futures'])

            # Get current price data
            current_data = gold_ticker.history(period="1d", interval="5m").tail(1)
            
            if current_data.empty:
                raise Exception("No current data available")
                
            current_price = float(current_data['Close'].iloc[-1])
            current_volume = float(current_data['Volume'].iloc[-1])
            
            # Calculate daily change
            daily_data = gold_ticker.history(period="2d", interval="1d")
            if len(daily_data) >= 2:
                prev_close = float(daily_data['Close'].iloc[-2])
                daily_change = current_price - prev_close
                daily_change_pct = (daily_change / prev_close) * 100
            else:
                daily_change = 0
                daily_change_pct = 0
                
            # Get additional market data
            market_data = await self._get_market_context()
            
            result = {
                'price': current_price,
                'volume': current_volume,
                'daily_change': daily_change,
                'daily_change_pct': daily_change_pct,
                'timestamp': datetime.now().isoformat(),
                'market_context': market_data,
                'symbol': self.symbols['gold_futures']
            }
            
            self.cache['current'] = result
            return result
            
        except Exception as e:
            logger.error(f"Error getting current price: {e}")
            # Return cached data if available
            if 'current' in self.cache:
                return self.cache['current']

            # Return demo data if no cache available
            return self._get_demo_data()
    
    async def get_historical_data(self, period: str = "1mo", interval: str = "1h") -> pd.DataFrame:
        """Get historical gold data with technical indicators"""
        try:
            # Get main gold data
            gold_ticker = yf.Ticker(self.symbols['gold_futures'])
            data = gold_ticker.history(period=period, interval=interval)
            
            if data.empty:
                raise Exception("No historical data available")
            
            # Add technical indicators
            data = self._add_technical_indicators(data)
            
            # Add market context data
            market_data = await self._get_historical_market_data(period, interval)
            data = pd.concat([data, market_data], axis=1)
            
            # Clean data
            data = data.dropna()
            
            self.cache[f'historical_{period}_{interval}'] = data
            return data
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            cache_key = f'historical_{period}_{interval}'
            if cache_key in self.cache:
                return self.cache[cache_key]

            # Return demo historical data
            return self._get_demo_historical_data(period, interval)
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add comprehensive technical indicators"""
        try:
            # Moving Averages
            data['SMA_20'] = data['Close'].rolling(window=20).mean()
            data['SMA_50'] = data['Close'].rolling(window=50).mean()
            data['SMA_200'] = data['Close'].rolling(window=200).mean()
            data['EMA_12'] = data['Close'].ewm(span=12).mean()
            data['EMA_26'] = data['Close'].ewm(span=26).mean()
            
            # MACD
            data['MACD'] = data['EMA_12'] - data['EMA_26']
            data['MACD_Signal'] = data['MACD'].ewm(span=9).mean()
            data['MACD_Histogram'] = data['MACD'] - data['MACD_Signal']
            
            # RSI
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['RSI'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            data['BB_Middle'] = data['Close'].rolling(window=20).mean()
            bb_std = data['Close'].rolling(window=20).std()
            data['BB_Upper'] = data['BB_Middle'] + (bb_std * 2)
            data['BB_Lower'] = data['BB_Middle'] - (bb_std * 2)
            data['BB_Width'] = data['BB_Upper'] - data['BB_Lower']
            data['BB_Position'] = (data['Close'] - data['BB_Lower']) / data['BB_Width']
            
            # Stochastic
            low_14 = data['Low'].rolling(window=14).min()
            high_14 = data['High'].rolling(window=14).max()
            data['Stoch_K'] = 100 * ((data['Close'] - low_14) / (high_14 - low_14))
            data['Stoch_D'] = data['Stoch_K'].rolling(window=3).mean()
            
            # ATR (Average True Range)
            high_low = data['High'] - data['Low']
            high_close = np.abs(data['High'] - data['Close'].shift())
            low_close = np.abs(data['Low'] - data['Close'].shift())
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            data['ATR'] = true_range.rolling(window=14).mean()
            
            # Volume indicators
            data['Volume_SMA'] = data['Volume'].rolling(window=20).mean()
            data['Volume_Ratio'] = data['Volume'] / data['Volume_SMA']
            
            # Price momentum
            data['Momentum_10'] = data['Close'] / data['Close'].shift(10) - 1
            data['Momentum_20'] = data['Close'] / data['Close'].shift(20) - 1
            
            return data
            
        except Exception as e:
            logger.error(f"Error adding technical indicators: {e}")
            return data
    
    async def _get_market_context(self) -> Dict:
        """Get current market context data"""
        try:
            context = {}
            
            # Get DXY (Dollar Index)
            dxy_ticker = yf.Ticker(self.symbols['dxy'])
            dxy_data = dxy_ticker.history(period="1d", interval="1m").tail(1)
            if not dxy_data.empty:
                context['dxy'] = float(dxy_data['Close'].iloc[-1])
            
            # Get VIX (Volatility)
            vix_ticker = yf.Ticker(self.symbols['vix'])
            vix_data = vix_ticker.history(period="1d", interval="1m").tail(1)
            if not vix_data.empty:
                context['vix'] = float(vix_data['Close'].iloc[-1])
            
            # Get S&P 500
            sp_ticker = yf.Ticker(self.symbols['sp500'])
            sp_data = sp_ticker.history(period="1d", interval="1m").tail(1)
            if not sp_data.empty:
                context['sp500'] = float(sp_data['Close'].iloc[-1])
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting market context: {e}")
            return {}
    
    async def _get_historical_market_data(self, period: str, interval: str) -> pd.DataFrame:
        """Get historical market context data"""
        try:
            market_data = pd.DataFrame()
            
            # Get DXY data
            dxy_ticker = yf.Ticker(self.symbols['dxy'])
            dxy_data = dxy_ticker.history(period=period, interval=interval)
            if not dxy_data.empty:
                market_data['DXY'] = dxy_data['Close']
            
            # Get VIX data
            vix_ticker = yf.Ticker(self.symbols['vix'])
            vix_data = vix_ticker.history(period=period, interval=interval)
            if not vix_data.empty:
                market_data['VIX'] = vix_data['Close']
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error getting historical market data: {e}")
            return pd.DataFrame()
    
    async def update_data(self):
        """Update all cached data"""
        try:
            logger.info("Updating data cache...")
            
            # Update current price
            await self.get_current_price()
            
            # Update historical data for different timeframes
            timeframes = [
                ("1d", "1m"),
                ("5d", "5m"),
                ("1mo", "1h"),
                ("3mo", "1d")
            ]
            
            for period, interval in timeframes:
                await self.get_historical_data(period, interval)
            
            self.last_update = datetime.now()
            logger.info("Data cache updated successfully")

        except Exception as e:
            logger.error(f"Error updating data: {e}")

    def _get_demo_data(self) -> Dict:
        """Get demo data when real data is not available"""
        import random

        # Generate realistic gold price around $2000
        base_price = 2000.0
        price_variation = random.uniform(-20, 20)
        current_price = base_price + price_variation

        daily_change = random.uniform(-15, 15)
        daily_change_pct = (daily_change / current_price) * 100

        return {
            'price': current_price,
            'volume': random.randint(50000, 200000),
            'daily_change': daily_change,
            'daily_change_pct': daily_change_pct,
            'timestamp': datetime.now().isoformat(),
            'market_context': {
                'dxy': 103.5 + random.uniform(-2, 2),
                'vix': 18.0 + random.uniform(-5, 5),
                'sp500': 4500 + random.uniform(-100, 100)
            },
            'symbol': 'DEMO_GOLD',
            'demo': True
        }

    def _get_demo_historical_data(self, period: str, interval: str) -> pd.DataFrame:
        """Generate demo historical data"""
        import random

        # Determine number of periods
        period_map = {
            '1d': 24, '5d': 120, '1mo': 720, '3mo': 2160
        }
        num_periods = period_map.get(period, 100)

        # Generate demo data
        dates = pd.date_range(end=datetime.now(), periods=num_periods, freq='H')

        # Generate realistic price data
        base_price = 2000.0
        prices = []
        current_price = base_price

        for i in range(num_periods):
            # Random walk with slight upward bias
            change = random.uniform(-0.5, 0.6)
            current_price += change
            prices.append(current_price)

        # Create DataFrame
        data = pd.DataFrame({
            'Open': prices,
            'High': [p + random.uniform(0, 2) for p in prices],
            'Low': [p - random.uniform(0, 2) for p in prices],
            'Close': prices,
            'Volume': [random.randint(10000, 100000) for _ in range(num_periods)]
        }, index=dates)

        # Add technical indicators
        data = self._add_technical_indicators(data)

        # Add demo market data
        data['DXY'] = [103.5 + random.uniform(-1, 1) for _ in range(num_periods)]
        data['VIX'] = [18.0 + random.uniform(-3, 3) for _ in range(num_periods)]

        return data
