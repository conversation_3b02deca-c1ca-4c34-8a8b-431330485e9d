/* Additional styles for Gold Trading AI System */

.advanced-chart {
    position: relative;
    height: 500px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1rem;
}

.indicator-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.indicator-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}

.indicator-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 0.5rem;
}

.indicator-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.signal-strength {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.strength-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff4444, #ffa500, #00ff00);
    transition: width 0.3s ease;
}

.trading-panel {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.position-calculator {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.calc-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    padding: 0.8rem;
    color: #ffffff;
    font-size: 1rem;
}

.calc-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.calc-result {
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid #ffd700;
    border-radius: 5px;
    padding: 1rem;
    text-align: center;
}

.news-feed {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 1rem;
}

.news-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    margin: 0.5rem 0;
    border-radius: 8px;
    border-left: 3px solid #ffd700;
}

.news-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.news-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.performance-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}

.performance-value {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.performance-positive { color: #00ff00; }
.performance-negative { color: #ff4444; }
.performance-neutral { color: #ffd700; }

.ai-insights {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.insight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.insight-icon {
    font-size: 1.5rem;
    color: #ffd700;
    margin-top: 0.2rem;
}

.insight-content {
    flex: 1;
}

.insight-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.insight-description {
    opacity: 0.9;
    line-height: 1.4;
}

.market-sentiment {
    text-align: center;
    padding: 1rem;
}

.sentiment-gauge {
    width: 200px;
    height: 100px;
    margin: 1rem auto;
    position: relative;
    background: conic-gradient(
        from 180deg,
        #ff4444 0deg 60deg,
        #ffa500 60deg 120deg,
        #00ff00 120deg 180deg
    );
    border-radius: 100px 100px 0 0;
}

.sentiment-needle {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 2px;
    height: 80px;
    background: #ffffff;
    transform-origin: bottom;
    transition: transform 0.5s ease;
}

.sentiment-label {
    margin-top: 1rem;
    font-size: 1.2rem;
    font-weight: bold;
}

.bullish { color: #00ff00; }
.bearish { color: #ff4444; }
.neutral { color: #ffa500; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .position-calculator {
        grid-template-columns: 1fr;
    }
    
    .performance-metrics {
        grid-template-columns: 1fr 1fr;
    }
    
    .indicator-panel {
        grid-template-columns: 1fr;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.pulse-glow {
    animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
}
