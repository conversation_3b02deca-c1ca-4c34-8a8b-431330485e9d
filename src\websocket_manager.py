"""
🔌 WebSocket Manager Module
Real-time communication with frontend
"""

import asyncio
import json
import logging
from typing import List, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manage WebSocket connections for real-time updates"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_data: Dict[WebSocket, Dict] = {}
    
    async def connect(self, websocket: WebSocket):
        """Accept new WebSocket connection"""
        try:
            await websocket.accept()
            self.active_connections.append(websocket)
            self.connection_data[websocket] = {
                'connected_at': asyncio.get_event_loop().time(),
                'message_count': 0
            }
            logger.info(f"New WebSocket connection. Total: {len(self.active_connections)}")
            
            # Send welcome message
            await self.send_personal_message({
                'type': 'welcome',
                'message': '🥇 Connected to Gold Trading AI System',
                'status': 'connected'
            }, websocket)
            
        except Exception as e:
            logger.error(f"Error connecting WebSocket: {e}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        try:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            if websocket in self.connection_data:
                del self.connection_data[websocket]
            logger.info(f"WebSocket disconnected. Total: {len(self.active_connections)}")
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {e}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send message to specific WebSocket connection"""
        try:
            if websocket in self.active_connections:
                await websocket.send_text(json.dumps(message))
                if websocket in self.connection_data:
                    self.connection_data[websocket]['message_count'] += 1
        except WebSocketDisconnect:
            self.disconnect(websocket)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        if not self.active_connections:
            return
        
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
                if connection in self.connection_data:
                    self.connection_data[connection]['message_count'] += 1
            except WebSocketDisconnect:
                disconnected.append(connection)
            except Exception as e:
                logger.error(f"Error broadcasting to connection: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def send_price_update(self, price_data: Dict):
        """Send price update to all clients"""
        message = {
            'type': 'price_update',
            'data': price_data,
            'timestamp': price_data.get('timestamp')
        }
        await self.broadcast(message)
    
    async def send_signal_update(self, signal_data: Dict):
        """Send trading signal update to all clients"""
        message = {
            'type': 'signal_update',
            'data': signal_data,
            'timestamp': signal_data.get('timestamp')
        }
        await self.broadcast(message)
    
    async def send_analysis_update(self, analysis_data: Dict):
        """Send analysis update to all clients"""
        message = {
            'type': 'analysis_update',
            'data': analysis_data,
            'timestamp': analysis_data.get('timestamp')
        }
        await self.broadcast(message)
    
    async def send_alert(self, alert_type: str, alert_message: str, severity: str = 'info'):
        """Send alert to all clients"""
        message = {
            'type': 'alert',
            'alert_type': alert_type,
            'message': alert_message,
            'severity': severity,
            'timestamp': asyncio.get_event_loop().time()
        }
        await self.broadcast(message)
    
    def get_connection_stats(self) -> Dict:
        """Get WebSocket connection statistics"""
        return {
            'total_connections': len(self.active_connections),
            'connection_details': [
                {
                    'connected_at': data['connected_at'],
                    'message_count': data['message_count']
                }
                for data in self.connection_data.values()
            ]
        }
