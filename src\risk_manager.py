"""
⚠️ Risk Manager Module
Advanced risk management and position sizing
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    VERY_LOW = "VERY_LOW"
    LOW = "LOW"
    MODERATE = "MODERATE"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"

class RiskManager:
    """Advanced risk management system"""
    
    def __init__(self):
        self.max_portfolio_risk = 0.02  # 2% max risk per trade
        self.max_daily_loss = 0.05      # 5% max daily loss
        self.max_drawdown = 0.15        # 15% max drawdown
        
        self.risk_multipliers = {
            RiskLevel.VERY_LOW: 0.5,
            RiskLevel.LOW: 0.75,
            RiskLevel.MODERATE: 1.0,
            RiskLevel.HIGH: 1.25,
            RiskLevel.VERY_HIGH: 1.5
        }
    
    async def calculate_risk_metrics(self, data: pd.DataFrame, signals: Dict) -> Dict:
        """Calculate comprehensive risk metrics"""
        try:
            logger.info("⚠️ Calculating risk metrics...")
            
            current_price = float(data['Close'].iloc[-1])
            
            # Calculate various risk metrics
            volatility_risk = await self._calculate_volatility_risk(data)
            market_risk = await self._calculate_market_risk(data, signals)
            position_risk = await self._calculate_position_risk(data, signals)
            portfolio_risk = await self._calculate_portfolio_risk(data, signals)
            
            # Calculate position sizing
            position_sizing = await self._calculate_position_sizing(data, signals, volatility_risk)
            
            # Risk assessment
            overall_risk = await self._assess_overall_risk(volatility_risk, market_risk, position_risk)
            
            # Risk recommendations
            recommendations = await self._generate_risk_recommendations(overall_risk, signals)
            
            result = {
                'current_price': current_price,
                'volatility_risk': volatility_risk,
                'market_risk': market_risk,
                'position_risk': position_risk,
                'portfolio_risk': portfolio_risk,
                'position_sizing': position_sizing,
                'overall_risk': overall_risk,
                'recommendations': recommendations,
                'risk_warnings': self._generate_risk_warnings(overall_risk, signals),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("✅ Risk metrics calculated successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {'error': str(e)}
    
    async def _calculate_volatility_risk(self, data: pd.DataFrame) -> Dict:
        """Calculate volatility-based risk metrics"""
        try:
            # ATR-based volatility
            if 'ATR' in data.columns:
                current_atr = float(data['ATR'].iloc[-1])
                avg_atr = float(data['ATR'].tail(20).mean())
                current_price = float(data['Close'].iloc[-1])
                
                atr_percentage = (current_atr / current_price) * 100
                volatility_ratio = current_atr / avg_atr
                
                # Classify volatility risk
                if atr_percentage > 3 or volatility_ratio > 2:
                    vol_risk_level = RiskLevel.VERY_HIGH
                elif atr_percentage > 2 or volatility_ratio > 1.5:
                    vol_risk_level = RiskLevel.HIGH
                elif atr_percentage > 1.5 or volatility_ratio > 1.2:
                    vol_risk_level = RiskLevel.MODERATE
                elif atr_percentage > 1 or volatility_ratio > 0.8:
                    vol_risk_level = RiskLevel.LOW
                else:
                    vol_risk_level = RiskLevel.VERY_LOW
            else:
                # Fallback: calculate simple volatility
                returns = data['Close'].pct_change().dropna()
                volatility = returns.std() * np.sqrt(252)  # Annualized
                
                if volatility > 0.4:
                    vol_risk_level = RiskLevel.VERY_HIGH
                elif volatility > 0.3:
                    vol_risk_level = RiskLevel.HIGH
                elif volatility > 0.2:
                    vol_risk_level = RiskLevel.MODERATE
                elif volatility > 0.15:
                    vol_risk_level = RiskLevel.LOW
                else:
                    vol_risk_level = RiskLevel.VERY_LOW
                
                atr_percentage = volatility * 100
                volatility_ratio = 1.0
                current_atr = data['Close'].iloc[-1] * volatility / 100
            
            return {
                'risk_level': vol_risk_level,
                'atr_percentage': atr_percentage,
                'volatility_ratio': volatility_ratio,
                'current_atr': current_atr,
                'description': self._get_risk_description(vol_risk_level)
            }
            
        except Exception as e:
            logger.error(f"Error calculating volatility risk: {e}")
            return {
                'risk_level': RiskLevel.MODERATE,
                'atr_percentage': 2.0,
                'volatility_ratio': 1.0,
                'current_atr': 0,
                'description': 'Unable to calculate'
            }
    
    async def _calculate_market_risk(self, data: pd.DataFrame, signals: Dict) -> Dict:
        """Calculate market-wide risk factors"""
        try:
            risk_factors = []
            risk_score = 0
            
            # VIX analysis (if available)
            if 'VIX' in data.columns:
                current_vix = float(data['VIX'].iloc[-1])
                if current_vix > 30:
                    risk_factors.append("High Fear Index (VIX > 30)")
                    risk_score += 2
                elif current_vix > 20:
                    risk_factors.append("Elevated Fear Index (VIX > 20)")
                    risk_score += 1
            
            # DXY strength (affects gold inversely)
            if 'DXY' in data.columns:
                dxy_change = (data['DXY'].iloc[-1] - data['DXY'].iloc[-5]) / data['DXY'].iloc[-5]
                if abs(dxy_change) > 0.02:
                    risk_factors.append(f"Strong USD Movement ({dxy_change*100:.1f}%)")
                    risk_score += 1
            
            # Volume analysis
            if 'Volume' in data.columns:
                current_volume = data['Volume'].iloc[-1]
                avg_volume = data['Volume'].tail(20).mean()
                volume_ratio = current_volume / avg_volume
                
                if volume_ratio < 0.5:
                    risk_factors.append("Low Volume (Liquidity Risk)")
                    risk_score += 1
                elif volume_ratio > 3:
                    risk_factors.append("Extremely High Volume (Volatility Risk)")
                    risk_score += 1
            
            # Price gaps
            if len(data) >= 2:
                gap = abs(data['Open'].iloc[-1] - data['Close'].iloc[-2]) / data['Close'].iloc[-2]
                if gap > 0.01:
                    risk_factors.append(f"Price Gap ({gap*100:.1f}%)")
                    risk_score += 1
            
            # Market conditions from signals
            if 'market_conditions' in signals:
                conditions = signals['market_conditions']
                if conditions.get('overall_rating') == 'Unfavorable':
                    risk_factors.append("Unfavorable Market Conditions")
                    risk_score += 1
                
                if conditions.get('volatility') == 'High':
                    risk_factors.append("High Market Volatility")
                    risk_score += 1
            
            # Determine overall market risk level
            if risk_score >= 5:
                market_risk_level = RiskLevel.VERY_HIGH
            elif risk_score >= 4:
                market_risk_level = RiskLevel.HIGH
            elif risk_score >= 2:
                market_risk_level = RiskLevel.MODERATE
            elif risk_score >= 1:
                market_risk_level = RiskLevel.LOW
            else:
                market_risk_level = RiskLevel.VERY_LOW
            
            return {
                'risk_level': market_risk_level,
                'risk_score': risk_score,
                'risk_factors': risk_factors,
                'description': self._get_risk_description(market_risk_level)
            }
            
        except Exception as e:
            logger.error(f"Error calculating market risk: {e}")
            return {
                'risk_level': RiskLevel.MODERATE,
                'risk_score': 2,
                'risk_factors': ['Unable to assess market risk'],
                'description': 'Unable to calculate'
            }
    
    async def _calculate_position_risk(self, data: pd.DataFrame, signals: Dict) -> Dict:
        """Calculate position-specific risk"""
        try:
            main_signal = signals.get('main_signal', {})
            signal_type = main_signal.get('signal')
            signal_confidence = main_signal.get('confidence', 0.5)
            
            # Signal confidence risk
            if signal_confidence < 0.3:
                confidence_risk = RiskLevel.VERY_HIGH
            elif signal_confidence < 0.5:
                confidence_risk = RiskLevel.HIGH
            elif signal_confidence < 0.7:
                confidence_risk = RiskLevel.MODERATE
            elif signal_confidence < 0.8:
                confidence_risk = RiskLevel.LOW
            else:
                confidence_risk = RiskLevel.VERY_LOW
            
            # Risk/Reward analysis
            risk_reward_ratio = 1.0
            if 'risk_levels' in signals and 'recommended' in signals['risk_levels']:
                recommended = signals['risk_levels']['recommended']
                risk_reward_ratio = recommended.get('risk_reward_ratio', 1.0)
            
            if risk_reward_ratio < 1:
                rr_risk = RiskLevel.VERY_HIGH
            elif risk_reward_ratio < 1.5:
                rr_risk = RiskLevel.HIGH
            elif risk_reward_ratio < 2:
                rr_risk = RiskLevel.MODERATE
            elif risk_reward_ratio < 3:
                rr_risk = RiskLevel.LOW
            else:
                rr_risk = RiskLevel.VERY_LOW
            
            # Support/Resistance proximity
            sr_risk = RiskLevel.MODERATE
            if 'entry_exit' in signals:
                entry_exit = signals['entry_exit']
                current_price = entry_exit.get('current_price', 0)
                
                # Check proximity to support/resistance
                if 'support_resistance' in signals.get('analysis', {}):
                    sr_data = signals['analysis']['support_resistance']
                    resistance_levels = sr_data.get('resistance_levels', [])
                    support_levels = sr_data.get('support_levels', [])
                    
                    # Check if too close to resistance (for buy signals)
                    if signal_type in ['BUY', 'STRONG_BUY'] and resistance_levels:
                        nearest_resistance = min(resistance_levels)
                        distance_to_resistance = (nearest_resistance - current_price) / current_price
                        if distance_to_resistance < 0.02:  # Less than 2% to resistance
                            sr_risk = RiskLevel.HIGH
                    
                    # Check if too close to support (for sell signals)
                    elif signal_type in ['SELL', 'STRONG_SELL'] and support_levels:
                        nearest_support = max(support_levels)
                        distance_to_support = (current_price - nearest_support) / current_price
                        if distance_to_support < 0.02:  # Less than 2% to support
                            sr_risk = RiskLevel.HIGH
            
            # Overall position risk
            risk_scores = {
                RiskLevel.VERY_LOW: 1,
                RiskLevel.LOW: 2,
                RiskLevel.MODERATE: 3,
                RiskLevel.HIGH: 4,
                RiskLevel.VERY_HIGH: 5
            }
            
            avg_risk_score = (
                risk_scores[confidence_risk] + 
                risk_scores[rr_risk] + 
                risk_scores[sr_risk]
            ) / 3
            
            if avg_risk_score >= 4.5:
                position_risk_level = RiskLevel.VERY_HIGH
            elif avg_risk_score >= 3.5:
                position_risk_level = RiskLevel.HIGH
            elif avg_risk_score >= 2.5:
                position_risk_level = RiskLevel.MODERATE
            elif avg_risk_score >= 1.5:
                position_risk_level = RiskLevel.LOW
            else:
                position_risk_level = RiskLevel.VERY_LOW
            
            return {
                'risk_level': position_risk_level,
                'confidence_risk': confidence_risk,
                'risk_reward_risk': rr_risk,
                'support_resistance_risk': sr_risk,
                'signal_confidence': signal_confidence,
                'risk_reward_ratio': risk_reward_ratio,
                'description': self._get_risk_description(position_risk_level)
            }
            
        except Exception as e:
            logger.error(f"Error calculating position risk: {e}")
            return {
                'risk_level': RiskLevel.MODERATE,
                'confidence_risk': RiskLevel.MODERATE,
                'risk_reward_risk': RiskLevel.MODERATE,
                'support_resistance_risk': RiskLevel.MODERATE,
                'signal_confidence': 0.5,
                'risk_reward_ratio': 1.0,
                'description': 'Unable to calculate'
            }
    
    async def _calculate_portfolio_risk(self, data: pd.DataFrame, signals: Dict) -> Dict:
        """Calculate portfolio-level risk metrics"""
        try:
            # This would ideally connect to actual portfolio data
            # For now, we'll provide theoretical calculations
            
            current_price = float(data['Close'].iloc[-1])
            
            # Theoretical portfolio metrics
            portfolio_value = 100000  # $100k theoretical portfolio
            max_position_size = portfolio_value * 0.1  # Max 10% per position
            recommended_position_size = portfolio_value * 0.05  # Recommended 5%
            
            # Risk per trade calculations
            if 'risk_levels' in signals and 'recommended' in signals['risk_levels']:
                recommended = signals['risk_levels']['recommended']
                stop_loss = recommended.get('stop_loss', current_price * 0.98)
                
                risk_per_share = abs(current_price - stop_loss)
                max_shares = max_position_size / current_price
                recommended_shares = recommended_position_size / current_price
                
                max_risk_amount = risk_per_share * max_shares
                recommended_risk_amount = risk_per_share * recommended_shares
                
                max_risk_percentage = (max_risk_amount / portfolio_value) * 100
                recommended_risk_percentage = (recommended_risk_amount / portfolio_value) * 100
            else:
                max_risk_percentage = 2.0
                recommended_risk_percentage = 1.0
                max_risk_amount = portfolio_value * 0.02
                recommended_risk_amount = portfolio_value * 0.01
            
            # Risk level assessment
            if recommended_risk_percentage > 5:
                portfolio_risk_level = RiskLevel.VERY_HIGH
            elif recommended_risk_percentage > 3:
                portfolio_risk_level = RiskLevel.HIGH
            elif recommended_risk_percentage > 2:
                portfolio_risk_level = RiskLevel.MODERATE
            elif recommended_risk_percentage > 1:
                portfolio_risk_level = RiskLevel.LOW
            else:
                portfolio_risk_level = RiskLevel.VERY_LOW
            
            return {
                'risk_level': portfolio_risk_level,
                'theoretical_portfolio_value': portfolio_value,
                'max_position_size': max_position_size,
                'recommended_position_size': recommended_position_size,
                'max_risk_percentage': max_risk_percentage,
                'recommended_risk_percentage': recommended_risk_percentage,
                'max_risk_amount': max_risk_amount,
                'recommended_risk_amount': recommended_risk_amount,
                'description': self._get_risk_description(portfolio_risk_level)
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return {
                'risk_level': RiskLevel.MODERATE,
                'theoretical_portfolio_value': 100000,
                'max_position_size': 10000,
                'recommended_position_size': 5000,
                'max_risk_percentage': 2.0,
                'recommended_risk_percentage': 1.0,
                'max_risk_amount': 2000,
                'recommended_risk_amount': 1000,
                'description': 'Unable to calculate'
            }
    
    async def _calculate_position_sizing(self, data: pd.DataFrame, signals: Dict, volatility_risk: Dict) -> Dict:
        """Calculate optimal position sizing"""
        try:
            current_price = float(data['Close'].iloc[-1])
            vol_risk_level = volatility_risk.get('risk_level', RiskLevel.MODERATE)
            
            # Base position size (as percentage of portfolio)
            base_position_pct = 0.05  # 5%
            
            # Adjust for volatility
            vol_multiplier = self.risk_multipliers.get(vol_risk_level, 1.0)
            adjusted_position_pct = base_position_pct / vol_multiplier
            
            # Adjust for signal confidence
            main_signal = signals.get('main_signal', {})
            signal_confidence = main_signal.get('confidence', 0.5)
            confidence_multiplier = 0.5 + (signal_confidence * 0.5)  # 0.5 to 1.0
            
            final_position_pct = adjusted_position_pct * confidence_multiplier
            
            # Cap at maximum limits
            final_position_pct = min(final_position_pct, 0.1)  # Max 10%
            final_position_pct = max(final_position_pct, 0.01)  # Min 1%
            
            # Calculate actual sizes for different portfolio values
            portfolio_sizes = [10000, 25000, 50000, 100000, 250000, 500000]
            position_sizes = {}
            
            for portfolio_value in portfolio_sizes:
                position_value = portfolio_value * final_position_pct
                shares = position_value / current_price
                position_sizes[f"${portfolio_value:,}"] = {
                    'position_value': position_value,
                    'shares': shares,
                    'percentage': final_position_pct * 100
                }
            
            return {
                'recommended_position_percentage': final_position_pct * 100,
                'volatility_adjustment': vol_multiplier,
                'confidence_adjustment': confidence_multiplier,
                'position_sizes_by_portfolio': position_sizes,
                'risk_level': vol_risk_level,
                'notes': [
                    f"Adjusted for {vol_risk_level.value} volatility",
                    f"Signal confidence: {signal_confidence:.1%}",
                    "Position size capped at 10% max, 1% min"
                ]
            }
            
        except Exception as e:
            logger.error(f"Error calculating position sizing: {e}")
            return {'error': str(e)}
    
    async def _assess_overall_risk(self, volatility_risk: Dict, market_risk: Dict, position_risk: Dict) -> Dict:
        """Assess overall risk level"""
        try:
            risk_levels = [
                volatility_risk.get('risk_level', RiskLevel.MODERATE),
                market_risk.get('risk_level', RiskLevel.MODERATE),
                position_risk.get('risk_level', RiskLevel.MODERATE)
            ]
            
            risk_scores = {
                RiskLevel.VERY_LOW: 1,
                RiskLevel.LOW: 2,
                RiskLevel.MODERATE: 3,
                RiskLevel.HIGH: 4,
                RiskLevel.VERY_HIGH: 5
            }
            
            # Calculate weighted average (volatility has higher weight)
            weights = [0.4, 0.3, 0.3]  # volatility, market, position
            weighted_score = sum(risk_scores[level] * weight for level, weight in zip(risk_levels, weights))
            
            if weighted_score >= 4.5:
                overall_risk = RiskLevel.VERY_HIGH
            elif weighted_score >= 3.5:
                overall_risk = RiskLevel.HIGH
            elif weighted_score >= 2.5:
                overall_risk = RiskLevel.MODERATE
            elif weighted_score >= 1.5:
                overall_risk = RiskLevel.LOW
            else:
                overall_risk = RiskLevel.VERY_LOW
            
            return {
                'risk_level': overall_risk,
                'risk_score': weighted_score,
                'component_risks': {
                    'volatility': volatility_risk.get('risk_level'),
                    'market': market_risk.get('risk_level'),
                    'position': position_risk.get('risk_level')
                },
                'description': self._get_risk_description(overall_risk)
            }
            
        except Exception as e:
            logger.error(f"Error assessing overall risk: {e}")
            return {
                'risk_level': RiskLevel.MODERATE,
                'risk_score': 3.0,
                'component_risks': {},
                'description': 'Unable to assess'
            }
    
    async def _generate_risk_recommendations(self, overall_risk: Dict, signals: Dict) -> List[str]:
        """Generate risk management recommendations"""
        try:
            recommendations = []
            risk_level = overall_risk.get('risk_level', RiskLevel.MODERATE)
            
            # General recommendations based on risk level
            if risk_level == RiskLevel.VERY_HIGH:
                recommendations.extend([
                    "🚨 VERY HIGH RISK - Consider avoiding this trade",
                    "If trading, use minimum position size (1% or less)",
                    "Set very tight stop losses",
                    "Monitor position constantly",
                    "Consider waiting for better market conditions"
                ])
            elif risk_level == RiskLevel.HIGH:
                recommendations.extend([
                    "⚠️ HIGH RISK - Trade with extreme caution",
                    "Use reduced position size (2-3% max)",
                    "Set tight stop losses",
                    "Monitor position closely",
                    "Consider partial profit taking"
                ])
            elif risk_level == RiskLevel.MODERATE:
                recommendations.extend([
                    "📊 MODERATE RISK - Standard risk management applies",
                    "Use normal position size (3-5%)",
                    "Set appropriate stop losses",
                    "Monitor position regularly"
                ])
            elif risk_level == RiskLevel.LOW:
                recommendations.extend([
                    "✅ LOW RISK - Favorable conditions",
                    "Can use standard to slightly larger position size",
                    "Set normal stop losses",
                    "Good opportunity for swing trading"
                ])
            else:  # VERY_LOW
                recommendations.extend([
                    "🎯 VERY LOW RISK - Excellent conditions",
                    "Can use larger position size (up to 7-8%)",
                    "Set wider stop losses for trend following",
                    "Good opportunity for position trading"
                ])
            
            # Signal-specific recommendations
            main_signal = signals.get('main_signal', {})
            signal_type = main_signal.get('signal')
            signal_confidence = main_signal.get('confidence', 0.5)
            
            if signal_confidence < 0.5:
                recommendations.append("⚠️ Low signal confidence - consider waiting for stronger signals")
            elif signal_confidence > 0.8:
                recommendations.append("✅ High signal confidence - good trade setup")
            
            # Market condition recommendations
            if 'market_conditions' in signals:
                conditions = signals['market_conditions']
                if conditions.get('volatility') == 'High':
                    recommendations.append("📈 High volatility - use tighter stops and smaller positions")
                if conditions.get('volume') == 'Low':
                    recommendations.append("📉 Low volume - be cautious of liquidity issues")
            
            return recommendations[:8]  # Limit to 8 recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Unable to generate recommendations"]
    
    def _generate_risk_warnings(self, overall_risk: Dict, signals: Dict) -> List[str]:
        """Generate risk warnings"""
        try:
            warnings = []
            risk_level = overall_risk.get('risk_level', RiskLevel.MODERATE)
            
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
                warnings.append("⚠️ HIGH RISK TRADE - Only risk what you can afford to lose")
            
            # Check for specific warning conditions
            main_signal = signals.get('main_signal', {})
            signal_confidence = main_signal.get('confidence', 0.5)
            
            if signal_confidence < 0.3:
                warnings.append("🚨 Very low signal confidence - high probability of false signal")
            
            # Market warnings
            if 'market_conditions' in signals:
                conditions = signals['market_conditions']
                if conditions.get('overall_rating') == 'Unfavorable':
                    warnings.append("⚠️ Unfavorable market conditions detected")
                if conditions.get('volatility') == 'High':
                    warnings.append("📊 High volatility - expect large price swings")
            
            # Add general trading warnings
            warnings.extend([
                "💡 Past performance does not guarantee future results",
                "📚 Always use proper risk management",
                "🎯 Never risk more than you can afford to lose"
            ])
            
            return warnings[:5]  # Limit to 5 warnings
            
        except Exception as e:
            logger.error(f"Error generating warnings: {e}")
            return ["⚠️ Unable to assess risk warnings"]
    
    def _get_risk_description(self, risk_level: RiskLevel) -> str:
        """Get human-readable risk description"""
        descriptions = {
            RiskLevel.VERY_LOW: "Very low risk - Excellent trading conditions",
            RiskLevel.LOW: "Low risk - Good trading conditions",
            RiskLevel.MODERATE: "Moderate risk - Standard trading conditions",
            RiskLevel.HIGH: "High risk - Caution advised",
            RiskLevel.VERY_HIGH: "Very high risk - Extreme caution required"
        }
        return descriptions.get(risk_level, "Unknown risk level")
