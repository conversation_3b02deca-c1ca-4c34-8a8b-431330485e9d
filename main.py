"""
🥇 GOLD TRADING AI SYSTEM 🥇
Advanced AI-powered Gold Trading Analysis System
Created by: Augment Agent
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import uvicorn
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import pandas as pd
import numpy as np

# Import our custom modules
from src.data_collector import GoldDataCollector
from src.ai_analyzer import AIAnalyzer
from src.signal_generator import SignalGenerator
from src.risk_manager import RiskManager
from src.websocket_manager import WebSocketManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="🥇 Gold Trading AI System",
    description="Advanced AI-powered Gold Trading Analysis System",
    version="1.0.0"
)

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Initialize components
data_collector = GoldDataCollector()
ai_analyzer = AIAnalyzer()
signal_generator = SignalGenerator()
risk_manager = RiskManager()
websocket_manager = WebSocketManager()

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Main dashboard page"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/api/gold/current")
async def get_current_gold_price():
    """Get current gold price and basic info"""
    try:
        current_data = await data_collector.get_current_price()
        return {
            "success": True,
            "data": current_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting current gold price: {e}")
        return {"success": False, "error": str(e)}

@app.get("/api/analysis/complete")
async def get_complete_analysis():
    """Get complete AI analysis with signals"""
    try:
        # Get latest data
        data = await data_collector.get_historical_data(period="1mo")
        
        # Run AI analysis
        analysis = await ai_analyzer.analyze_complete(data)
        
        # Generate signals
        signals = await signal_generator.generate_signals(data, analysis)
        
        # Calculate risk management
        risk_data = await risk_manager.calculate_risk_metrics(data, signals)
        
        return {
            "success": True,
            "data": {
                "analysis": analysis,
                "signals": signals,
                "risk": risk_data,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Error in complete analysis: {e}")
        return {"success": False, "error": str(e)}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # Send real-time updates every 5 seconds
            await asyncio.sleep(5)
            
            # Get latest analysis
            try:
                data = await data_collector.get_current_price()
                quick_analysis = await ai_analyzer.quick_analysis(data)
                
                await websocket_manager.send_personal_message({
                    "type": "update",
                    "data": quick_analysis,
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                
            except Exception as e:
                logger.error(f"Error in websocket update: {e}")
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

@app.on_event("startup")
async def startup_event():
    """Initialize system on startup"""
    logger.info("🚀 Starting Gold Trading AI System...")
    
    # Initialize AI models
    await ai_analyzer.initialize_models()
    
    # Start background tasks
    asyncio.create_task(background_data_collection())
    
    logger.info("✅ System initialized successfully!")

async def background_data_collection():
    """Background task for continuous data collection"""
    while True:
        try:
            await data_collector.update_data()
            await asyncio.sleep(60)  # Update every minute
        except Exception as e:
            logger.error(f"Error in background data collection: {e}")
            await asyncio.sleep(60)

if __name__ == "__main__":
    print("🥇 GOLD TRADING AI SYSTEM 🥇")
    print("=" * 50)
    print("Starting advanced AI trading system...")
    print("Dashboard will be available at: http://localhost:8000")
    print("=" * 50)
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
