"""
⚙️ Configuration Module
System configuration and settings
"""

import os
from typing import Dict, Any

class Config:
    """Application configuration"""
    
    # API Keys (set these in environment variables)
    ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY', 'demo')
    NEWS_API_KEY = os.getenv('NEWS_API_KEY', 'demo')
    
    # Database settings
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///gold_trading.db')
    
    # Trading settings
    DEFAULT_RISK_PERCENTAGE = 0.02  # 2% risk per trade
    MAX_POSITION_SIZE = 0.10        # 10% max position size
    DEFAULT_STOP_LOSS_ATR = 2.0     # 2x ATR for stop loss
    DEFAULT_TAKE_PROFIT_ATR = 4.0   # 4x ATR for take profit
    
    # Data collection settings
    DATA_UPDATE_INTERVAL = 60       # seconds
    WEBSOCKET_UPDATE_INTERVAL = 5   # seconds
    CACHE_EXPIRY = 300             # 5 minutes
    
    # AI Model settings
    LSTM_SEQUENCE_LENGTH = 60
    LSTM_EPOCHS = 100
    LSTM_BATCH_SIZE = 32
    MODEL_RETRAIN_INTERVAL = 86400  # 24 hours
    
    # Risk management
    MAX_DAILY_LOSS = 0.05          # 5% max daily loss
    MAX_DRAWDOWN = 0.15            # 15% max drawdown
    POSITION_SIZE_MULTIPLIERS = {
        'VERY_LOW': 1.5,
        'LOW': 1.2,
        'MODERATE': 1.0,
        'HIGH': 0.8,
        'VERY_HIGH': 0.5
    }
    
    # Technical indicators
    RSI_PERIOD = 14
    MACD_FAST = 12
    MACD_SLOW = 26
    MACD_SIGNAL = 9
    BB_PERIOD = 20
    BB_STD = 2
    ATR_PERIOD = 14
    
    # Market data symbols
    SYMBOLS = {
        'gold_futures': 'GC=F',
        'gold_etf': 'GLD',
        'xauusd': 'XAUUSD=X',
        'dxy': 'DX-Y.NYB',
        'vix': '^VIX',
        'sp500': '^GSPC',
        'bonds': '^TNX'
    }
    
    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Server settings
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', 8000))
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    @classmethod
    def get_all_settings(cls) -> Dict[str, Any]:
        """Get all configuration settings"""
        return {
            key: getattr(cls, key)
            for key in dir(cls)
            if not key.startswith('_') and not callable(getattr(cls, key))
        }

# Development configuration
class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    DATA_UPDATE_INTERVAL = 30
    WEBSOCKET_UPDATE_INTERVAL = 3

# Production configuration
class ProductionConfig(Config):
    DEBUG = False
    LOG_LEVEL = 'WARNING'
    DATA_UPDATE_INTERVAL = 60
    WEBSOCKET_UPDATE_INTERVAL = 5

# Testing configuration
class TestingConfig(Config):
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    DATABASE_URL = 'sqlite:///test_gold_trading.db'
    DATA_UPDATE_INTERVAL = 10
    WEBSOCKET_UPDATE_INTERVAL = 1

# Configuration factory
def get_config(env: str = None) -> Config:
    """Get configuration based on environment"""
    env = env or os.getenv('ENVIRONMENT', 'development')
    
    configs = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'testing': TestingConfig
    }
    
    return configs.get(env, DevelopmentConfig)
